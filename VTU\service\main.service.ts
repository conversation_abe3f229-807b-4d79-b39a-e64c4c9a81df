import { v4 as uuidv4 } from "uuid";
import { AnchorVTUService } from "./anchor.service";
import { VTURepository } from "../repository/vtu.repository";
import { VTURepository as VTURepository2 } from "../repository/vtu.repository2";
import { VTUValidation } from "../validations/vtu.validation";
import { VTU_CONFIG, getNetworkFromPhone } from "../config/vtu.config";
import {
  AirtimeRequest,
  AirtimeResponse,
  AnchorAirtimeRequest
} from "../interfaces/airtime.interface";
import WalletService from "../../services/wallet/WalletService";

import AppException from "../../AppException";
import { domainError } from "../../domainError";
import AnchorModel from "../../shared/Model/AnchorModel";
import TransactionLogModel from "../../shared/Model/TransactionLogModel";

export class VTUService {
  private anchorService: AnchorVTUService;
  private vtuRepository: VTURepository;
  private vtuRepository2: VTURepository2;

  constructor() {
    this.anchorService = new AnchorVTUService();
    this.vtuRepository = new VTURepository();
    this.vtuRepository2 = new VTURepository2();
  }

  async purchaseAirtime(request: AirtimeRequest): Promise<AirtimeResponse> {
    try {
      console.log("Entered purchaseAirtime service method.");

      const validation = VTUValidation.validateAirtimeRequest({
        ...request,
        network: undefined
      });
      if (validation.error) {
        return {
          success: false,
          message: validation.error.details[0].message
        };
      }
      const phoneValidation = VTUValidation.validatePhoneNumber(request.phoneNumber);
      if (!phoneValidation.isValid) {
        return {
          success: false,
          message: phoneValidation.error || "Invalid phone number"
        };
      }

      const network = getNetworkFromPhone(request.phoneNumber);
      if (!network || network === "UNKNOWN") {
        return {
          success: false,
          message: "Unable to detect network provider from phone number"
        };
      }

      console.log(`Detected network: ${network}`);

      const amountValidation = VTUValidation.validateAmount(request.amount);
      if (!amountValidation.isValid) {
        return {
          success: false,
          message: amountValidation.error || "Invalid amount"
        };
      }

      // get user anchor
      // confirm deposit id
      // get user wallet
      const senderAnchor = await AnchorModel.findOne({ where: { userId: request.userId } });
      if (!senderAnchor?.depositAccountId)
        throw new AppException(
          domainError.TEST_ERROR,
          "your account setup is not complete and can not run transaction"
        );

      const walletService = new WalletService();
      const walletResponse = await walletService.getUserWallet(request.userId);
      if (!walletResponse || !walletResponse.wallet) {
        return {
          success: false,
          message: "User wallet not found"
        };
      }
      const userWallet = walletResponse.wallet;

      if (userWallet.balance < request.amount) {
        return {
          success: false,
          message: "Insufficient wallet balance"
        };
      }

      const reference = `AIRTIME_${Date.now()}_${uuidv4().substring(0, 8)}`;

      const transaction = await this.vtuRepository.createTransaction({
        userId: request.userId,
        serviceType: VTU_CONFIG.SERVICE_TYPES.AIRTIME,
        phoneNumber: phoneValidation.formatted,
        amount: request.amount,
        network,
        reference,
        status: VTU_CONFIG.TRANSACTION_STATUS.PROCESSING
      });

      const networkConfig =
        VTU_CONFIG.ANCHOR.NETWORKS[network as keyof typeof VTU_CONFIG.ANCHOR.NETWORKS];
      console.log(`[Debug] Network Config found:`, networkConfig);

      if (!networkConfig) {
        await this.vtuRepository.updateTransaction(transaction.id, {
          status: VTU_CONFIG.TRANSACTION_STATUS.FAILED,
          anchorResponse: "Unsupported network provider"
        });
        return {
          success: false,
          message: "Unsupported network provider"
        };
      }

      const anchorRequest: AnchorAirtimeRequest = {
        //
        data: {
          type: "Airtime",
          attributes: {
            phoneNumber: phoneValidation.formatted,
            amount: request.amount * 100, // Convert to kobo
            reference: reference,
            provider: network.toLowerCase()
          },
          relationships: {
            account: {
              data: {
                type: "DepositAccount",
                id: senderAnchor.depositAccountId
              }
            }
          }
        }
      };
      const anchorResponse = await this.anchorService.purchaseAirtime(anchorRequest);

      // Check for the presence of the 'data' object to determine success
      const isSuccess = !!anchorResponse.data;

      const status = isSuccess
        ? VTU_CONFIG.TRANSACTION_STATUS.SUCCESSFUL
        : VTU_CONFIG.TRANSACTION_STATUS.FAILED;

      let anchorResponseString: string;
      try {
        anchorResponseString = JSON.stringify(anchorResponse);
      } catch {
        anchorResponseString = "Error parsing Anchor response";
      }

      await this.vtuRepository.updateTransaction(transaction.id, {
        status,
        providerReference: anchorResponse.data?.ident,
        anchorResponse: anchorResponseString
      });

      let transaction2: any;
      let wallet: any;

      if (isSuccess) {
        try {
          transaction2 = await this.vtuRepository2.createTransaction({
            transactionType: "AIRTIME",
            amount: request.amount,
            status: "SUCCESS",
            initiatingWalletId: userWallet.id,
            userId: request.userId,
            phoneNumber: phoneValidation.formatted,
            network,
            provider: network.toLowerCase(),
            reference,
            providerReference: anchorResponse.data?.ident,
            anchorResponse: anchorResponseString
          });

          wallet = (await await walletService.withdraw(userWallet.id, request.amount)).wallet;
          await TransactionLogModel.create({
            transactionId: transaction2.id,
            walletId: userWallet.id,
            entryType: "DEBIT",
            amount: request.amount,
            transactionType: "AIRTIME",
            transactionStatus: "SUCCESSFUL",
            paymentMethod: "WALLET",
            statement: "airtime purchase",
            balance: wallet.balance
          });
        } catch (e) {
          const error: any = e;
          console.error("Wallet withdrawal failed after successful VTU:", error.message);
        }
        return {
          success: true,
          message: "Airtime purchase successful",
          data: {
            reference,
            status: "SUCCESSFUL",
            amount: request.amount,
            phoneNumber: phoneValidation.formatted,
            network,
            transactionId: transaction.id,
            created_at: transaction2.created_at,
            wallet: {
              id: wallet.id,
              balance: wallet.balance
            }
          }
        };
      } else {
        return {
          success: false,
          message: anchorResponse.message || "Airtime purchase failed",
          error: anchorResponse.message
        };
      }
    } catch (e) {
      const error: any = e;
      console.error("VTU Service Error:", error.message);
      return {
        success: false,
        message: "An error occurred while processing your request",
        error: error.message
      };
    }
  }

  async getTransactionStatus(reference: string): Promise<AirtimeResponse> {
    try {
      const transaction = await this.vtuRepository.getTransactionByReference(reference);
      if (!transaction) {
        return {
          success: false,
          message: "Transaction not found"
        };
      }
      if (
        transaction.status === VTU_CONFIG.TRANSACTION_STATUS.PROCESSING ||
        transaction.status === VTU_CONFIG.TRANSACTION_STATUS.PENDING
      ) {
        const anchorResponse = await this.anchorService.verifyTransaction(reference);
        if (anchorResponse.status !== undefined) {
          const status = anchorResponse.status
            ? VTU_CONFIG.TRANSACTION_STATUS.SUCCESSFUL
            : VTU_CONFIG.TRANSACTION_STATUS.FAILED;
          await this.vtuRepository.updateTransaction(transaction.id, {
            status,
            anchorResponse: JSON.stringify(anchorResponse)
          });
          transaction.status = status;
        }
      }
      return {
        success: true,
        message: "Transaction status retrieved",
        data: {
          reference: transaction.reference || "",
          status: transaction.status,
          amount: Number(transaction.amount),
          phoneNumber: transaction.phoneNumber,
          network: transaction.network,
          transactionId: transaction.id,
          created_at: transaction.created_at.toISOString()
        }
      };
    } catch (e) {
      const error: any = e;
      console.error("Get Transaction Status Error:", error.message);
      return {
        success: false,
        message: "Failed to get transaction status",
        error: error.message
      };
    }
  }

  async getUserTransactions(userId: string, limit: number = 50) {
    try {
      const transactions = await this.vtuRepository.getUserTransactions(userId, limit);
      return {
        success: true,
        message: "Transactions retrieved successfully",
        data: transactions.map(tx => ({
          id: tx.id,
          reference: tx.reference,
          serviceType: tx.serviceType,
          phoneNumber: tx.phoneNumber,
          amount: Number(tx.amount),
          network: tx.network,
          status: tx.status,
          createdAt: tx.created_at,
          updatedAt: tx.updated_at
        }))
      };
    } catch (e) {
      const error: any = e;
      console.error("Get User Transactions Error:", error.message);
      return {
        success: false,
        message: "Failed to get user transactions",
        error: error.message
      };
    }
  }
}
