import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class Anchor extends Model<InferAttributes<Anchor>, InferCreationAttributes<Anchor>> {
  declare id: CreationOptional<string>;
  declare userId: string;
  declare customerId: string;

  declare virtualNubanId: string;
  declare bankId: string;
  declare bankName: string;
  declare bankNipCode: string;

  declare accountName: String;
  declare accountNumber: String;

  declare isDefault: String;
  declare isPermanent: String;
  declare status: String;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Anchor.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    customerId: {
      field: "customer_id",
      type: DataTypes.STRING
    },
    virtualNubanId: {
      field: "virtual_nuban_id",
      type: DataTypes.STRING
    },
    bankId: {
      field: "bank_id",
      type: DataTypes.STRING
    },
    bankName: {
      field: "bank_name",
      type: DataTypes.STRING
    },
    bankNipCode: {
      field: "bank_nip_code",
      type: DataTypes.STRING
    },

    accountName: {
      field: "account_name",
      type: DataTypes.STRING
    },
    accountNumber: {
      field: "account_number",
      type: DataTypes.STRING
    },

    isDefault: {
      field: "is_default",
      type: DataTypes.STRING
    },
    isPermanent: {
      field: "is_permanent",
      type: DataTypes.STRING
    },
    status: {
      field: "status",
      type: DataTypes.STRING
    },

    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "anchor_vn",
    tableName: "anchor_vn",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Anchor;
