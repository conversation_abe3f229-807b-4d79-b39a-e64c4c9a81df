'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('transaction_wallet2wallet', {
      transaction_id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      receiving_wallet_id: {
        type: Sequelize.UUID,
        allowNull: false
      },
      anchor_payment_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      anchor_payment_ref: {
        type: Sequelize.STRING,
        allowNull: true
      },
      reason: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ""
      }
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('transaction_wallet2wallet');
  }
};
