import IAccountService from "./IAccountService";
import AccountRepository from "./AccountRepository";

const accountRepository = new AccountRepository();

class AccountService implements IAccountService {
  async phoneExists(phone: string): Promise<boolean> {
    return await accountRepository.phoneExists(phone);
  }

  async emailExists(email: string): Promise<boolean> {
    return await accountRepository.emailExists(email);
  }
}

export default AccountService;
