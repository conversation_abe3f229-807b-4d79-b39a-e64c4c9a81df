import { Router } from "express";
import { Request, Response, NextFunction } from "express";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

import WalletPin from "./WalletPin";
import WalletView from "./WalletView";

import jwt from "../../libraries/jwt";
import WalletValidation from "./WalletValidation";

import WalletDeposit from "./WalletDeposit";
import WalletWithdraw from "./WalletWithdraw";

import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";
import WalletRepository from "./WalletRepository";

const router: Router = Router();

const walletRepository = new WalletRepository();

router.post("/test/deposit", async function(req: Request, res: Response, next: NextFunction) {
  try {
    const { wallet_id, amount } = req.body;

    const walletDeposit = new WalletDeposit(walletRepository);

    const action = await walletDeposit.init(wallet_id, amount);
    console.log(action);

    const wallet: any = action.wallet;

    const response: any = {};
    const statusCode = 200;
    const success = true;
    const message = "ok";
    const data: any = {};

    data["wallet"] = {};
    data["wallet"]["id"] = wallet.id;
    data["wallet"]["balance"] = wallet.balance;

    response.success = success;
    response.message = message;
    response.data = data;

    res.status(statusCode);
    res.json(response);
  } catch (err) {
    next(err);
  }
});

router.post("/test/withdraw", async function(req: Request, res: Response, next: NextFunction) {
  try {
    const { wallet_id, amount } = req.body;

    const walletWithdraw = new WalletWithdraw(walletRepository);

    const action = await walletWithdraw.init(wallet_id, amount);
    console.log(action);

    const wallet: any = action.wallet;

    const response: any = {};
    const statusCode = 200;
    const success = true;
    const message = "ok";
    const data: any = {};

    data["wallet"] = {};
    data["wallet"]["id"] = wallet.id;
    data["wallet"]["balance"] = wallet.balance;

    response.success = success;
    response.message = message;
    response.data = data;

    res.status(statusCode);
    res.json(response);
  } catch (err) {
    next(err);
  }
});

router.post("/pin/setup", AuthProtectionMiddleware, async function(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const { userId } = res.locals.authenticated_user;
    const { pin } = await WalletValidation.Pin(req.body);

    const walletPin = new WalletPin(walletRepository);
    const action = await walletPin.init(userId, pin);

    const response: any = {};
    const statusCode = 200;
    const success = true;
    const message = "ok";
    const data: any = {};

    response.success = success;
    response.message = message;
    response.data = data;

    res.status(statusCode);
    res.json(response);
  } catch (err) {
    next(err);
  }
});

export default router;
