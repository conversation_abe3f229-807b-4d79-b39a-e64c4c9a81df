import AppException from "../../AppException";
import { domainError } from "../../domainError";

import IAccountRepository from "./IAccountRepository";
import Account from "./Account";
import JWT from "../../libraries/jwt";
import Bcrypt from "../../libraries/bcrypt";
import jwt from "../../libraries/jwt";

import Usermodel from "../../shared/Model/UserModel";
import WalletService from "../../services/wallet/WalletService";
import AnchorService from "../../AnchorService";
import AnchorModel from "../../shared/Model/AnchorModel";
import AnchorVNModel from "../../shared/Model/AnchorVNModel";
import SessionModel from "../../shared/Model/SessionModel";

type DeviceInfo = {
  deviceId: string;
  deviceOS: string;
  deviceToken: string;
};

class AccountLogin {
  private loginId: string;
  private email: string;
  private password: string;

  private deviceInfo: DeviceInfo;

  private account: Account;
  private wallet: any;

  private _repository: IAccountRepository;

  constructor(accountRepository: IAccountRepository) {
    this._repository = accountRepository;
  }

  setEmail(email: string): void {
    this.email = email;
  }

  setLoginId(loginId: string): void {
    this.loginId = loginId;
  }

  setPassword(password: string): void {
    this.password = password;
  }

  setDeviceInfo(deviceId: string, deviceOS: string, deviceToken: string) {
    this.deviceInfo = { deviceId, deviceOS, deviceToken };
  }

  async authenticate(): Promise<boolean> {
    let isEmailAvailable;
    let isAuthenticated = false;

    return isAuthenticated;
  }

  private async issueNewSession() {
    let session;

    const issued_at: number = new Date().getTime();
    const expire_in: number = 1000 * 4; //1000 * 3600 * 24;

    const data = {
      id: this.account.id,
      firstname: this.account.firstname,
      lastname: this.account.lastname,
      email: this.account.email,
      phone: this.account.phone
    };

    let payload: any = {};
    payload.issued_at = issued_at;
    payload.expire_in = expire_in;
    payload.data = { userId: this.account.id, ...data };

    const token: string = await JWT.sign(payload);

    session = {
      userId: this.account.id,
      token,
      issued_at,
      expire_in
    };

    return session;
  }

  private async issueRefreshToken() {
    let refresh;

    const issued_at: number = new Date().getTime();
    const expire_in: number = 1000 * 3600 * 24 * 30;

    const data = { id: this.account.id, email: this.account.email };

    let payload: any = {};
    payload.issued_at = issued_at;
    payload.expire_in = expire_in;
    payload.data = { userId: this.account.id, ...data };

    const token: string = await JWT.sign(payload);

    refresh = {
      userId: this.account.id,
      token,
      issued_at,
      expire_in
    };

    return refresh;
  }

  async verifyPassword(password: any, accountPassword: any) {}

  async init() {
    const account: any = await this._repository.getAccountByEmail(this.loginId);
    if (!account) throw new AppException(domainError.INVALID_CREDENTIALS);

    let isPassword = await Bcrypt.compare(this.password, account.password);
    if (!isPassword) throw new AppException(domainError.INVALID_CREDENTIALS);

    if (account.deactivated)
      throw new AppException(
        domainError.TEST_ERROR,
        "Your account has been deactivated at your request. Please reach out to support to reactivate it."
      );

    this.account = account;

    let session: any = await this.issueNewSession();
    let refreshToken: any = await this.issueRefreshToken();
    let usv: any = await getUserVerbose(account.id);
    let userSession = await SessionModel.findOne({ where: { userId: account.id } });

    let deviceInfo: {
      deviceId?: string;
      deviceOS?: string;
      deviceToken?: string;
    } = {};
    if (this.deviceInfo.deviceId) deviceInfo["deviceId"] = this.deviceInfo.deviceId;
    if (this.deviceInfo.deviceId) deviceInfo["deviceOS"] = this.deviceInfo.deviceOS;
    if (this.deviceInfo.deviceId) deviceInfo["deviceToken"] = this.deviceInfo.deviceToken;

    if (!userSession)
      await SessionModel.create({
        userId: account.id,
        sessionToken: session.token,
        refreshToken: refreshToken.token,
        ...deviceInfo
      });
    else
      await SessionModel.update(
        {
          sessionToken: session.token,
          refreshToken: refreshToken.token,
          ...deviceInfo
        },
        { where: { userId: account.id } }
      );

    return { account: this.account, session: session, refreshToken: refreshToken, usv: usv };
  }

  async refresh(token: string) {
    const tokenData: any = await jwt.decode(token);
    if (!tokenData)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );

    if (tokenData.issued_at + tokenData.expire_in < new Date().getTime())
      throw new AppException(domainError.INVALID_OR_MISSING_HEADER, "invalid or expired token");

    const account: any = await Usermodel.findByPk(tokenData.data.userId);
    if (!account)
      throw new AppException(domainError.INVALID_OR_MISSING_HEADER, "invalid or expired token");
    if (account.deactivated)
      throw new AppException(
        domainError.TEST_ERROR,
        "Your account has been deactivated at your request. Please reach out to support to reactivate it."
      );

    const userSession = await SessionModel.findOne({ where: { userId: account.id } });
    if (!userSession)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );

    if (userSession.refreshToken !== token)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );

    this.account = account;

    let session: any = await this.issueNewSession();
    let refreshToken: any = await this.issueRefreshToken();
    let usv: any = await getUserVerbose(account.id);

    await SessionModel.update(
      { sessionToken: session.token, refreshToken: refreshToken.token },
      { where: { userId: account.id } }
    );

    return { account: this.account, session: session, refreshToken: refreshToken, usv: usv };
  }

  async updateDeviceToken(userId: string, token: string) {
    if (token) await SessionModel.update({ deviceToken: token }, { where: { userId: userId } });
  }
}

async function getUserVerbose(userId: string) {
  const walletService = new WalletService();

  const account = await Usermodel.findByPk(userId, {
    attributes: { exclude: ["password"] },
    raw: true
  });
  if (!account) throw new AppException(domainError.NOT_FOUND, "user does not exist");

  let anchor: any;
  let wallet: any;
  let virtualNuban: any;
  let vn: any;
  let isKYCVerified: any;

  wallet = (await walletService.getUserWallet(userId)).wallet;
  anchor = (await AnchorModel.findOne({ where: { userId: userId }, raw: true })) || {};
  isKYCVerified = anchor.status === "approved";
  vn = anchor?.defaultVirtualNuban
    ? await AnchorVNModel.findOne({
        where: { virtualNubanId: anchor.defaultVirtualNuban }
      })
    : {};
  virtualNuban = vn || {};

  delete virtualNuban?.userId;
  delete virtualNuban?.id;
  delete virtualNuban?.bankId;

  return {
    account: account,
    wallet: { id: wallet.id, balance: wallet.balance },
    virtualNuban,
    isKYCVerified: isKYCVerified
    // anchor: anchor,
  };
}

export default AccountLogin;
