import UUID from "../../../libraries/uuid";
import OTPModel from "../../../shared/Model/OTPModel";

class OTP {
  static async generate(data: any = {}) {
    const otpId = "otp_" + UUID.generate();
    const otpCode = OTP._generateOTPCode();

    await OTP._storeOTP(otpCode, otpId, data);
    return { otpCode, otpId };
  }

  static async retrieve(otpId: string) {
    let otp: any = await OTPModel.findOne({ where: { otp_id: otpId }, raw: true });
    if (!otp) return null;

    console.log(otp);

    let otpData = otp.data ? JSON.parse(otp.data) : {};
    return { ...otp, data: otpData };
  }

  private static _generateOTPCode(length = 6) {
    let code = "";
    for (let i = 0; i < length; i++) code += Math.floor(Math.random() * 9);
    return code;
  }

  private static async _storeOTP(code: string, id: string, data: object = {}) {
    await OTP._storeDB(code, id, data);
  }

  private static async _storeDB(code: string, id: string, data: object = {}) {
    await OTPModel.create({
      otp_id: id,
      otp_code: code,
      data: JSON.stringify(data),
      generated_time: new Date().getTime(),
      expiration_time: 300000
    });
  }
}

async function _storeRedis() {}

export default OTP;
