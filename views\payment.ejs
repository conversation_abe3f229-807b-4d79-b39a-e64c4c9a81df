<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-KK94CHFLLe+nY2dmCWGMq91rCGa5gtU4mk92HdvYe+M/SXH301p5ILy+dN9+nJOZ" crossorigin="anonymous">
    <title>Document</title>
</head>
<body>
    <wrap style="display: block; width: 700px; margin: auto;">

        <h1>
            Democredit
        </h1>


        <div id="view">
            <br>
            <h5>
                Fund Your Wallet You!
            </h5>
            <br />
            <form id="paymentForm">
                <div class="form-group">
                    <input type="tr_ref" class="form-control" id="tr-ref" hidden />
                </div>
                <div class="form-group" style="pointer-events: none; cursor: not-allowed; opacity: 0.7;">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" class="form-control" id="email-address"
                        style="pointer-events: none; cursor: not-allowed; opacity: 0.7;" />
                </div>
                <div class="form-group">
                    <label for="amount" class="form-label">Amount</label>
                    <input type="tel" class="form-control" id="amount" required />
                </div>
                <div class="form-group">
                    <label for="first-name" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="first-name" />
                </div>
                <div class="form-group">
                    <label for="last-name" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="last-name" />
                </div>
                <br>
                <div class="form-submit">
                    <button type="button" class="btn btn-primary" onclick="payWithPaystack(event)"> Proceed </button>
                </div>
            </form>
        </div>

        <wrap />

        <script>
            const curl = new URL(window.location.toLocaleString()).searchParams;
            const curl_ref = curl.get("ref");
            const curl_email = curl.get("email");

            document.getElementById('tr-ref').value = curl_ref;
            document.getElementById('email-address').value = curl_email;

            // Run a check to see if transaction refrence not completed to block duplicate
        </script>

        <script src="https://js.paystack.co/v1/inline.js"></script>
        <script>

            const PAYSTACK_PUBLIC_KEY = "<%= paystackPublic %>";
            const HOME_URL = "<%= homeUrl %>";


            function payWithPaystack(e) {
                e.preventDefault();

                const handler = PaystackPop.setup({
                    key: PAYSTACK_PUBLIC_KEY,
                    email: document.getElementById('email-address').value,
                    amount: document.getElementById('amount').value * 100,
                    currency: 'NGN',
                    ref: document.getElementById('tr-ref').value,
                    callback: function (response) {
                        console.log("____complete transaction____");

                        fetch(
                            `${HOME_URL}/transactions/fund/complete`,
                            {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json",
                                },
                                body: JSON.stringify({
                                    ref: document.getElementById('tr-ref').value,
                                }),
                            })
                            .then(response => response.json())
                            .then(data => {
                                const { transaction, wallet } = data.data
                                const { status, amount } = transaction

                                if (status === "success") alert(`Fund successful, your wallet has been credited with ${amount}`);
                                else alert(`could not complete fund at the moment`);

                                document.getElementById('view').innerHTML = "<br/><br/><h4>Arigato!</h4><br/><p>You can now close this tab</p>"
                            })
                    },
                    onClose: function () {
                    },
                });

                handler.openIframe();
            }
        </script>
</body>
</html>