import ITransactionRepository from "./ITransactionRepository";
import Transaction from "./Transaction";
import TransactionModel from "../../shared/Model/TransactionModel";
import TransactionWallet2WalletModel from "../../shared/Model/TransactionWallet2WalletModel";
import TransactionWallet2OtherModel from "../../shared/Model/TransactionWallet2OtherModel";
import TransactionWalletDepositModel from "../../shared/Model/TransactionWalletDepositModel";
import TransferRecipientModel from "../../shared/Model/TransferRecipientModel";
import TransactionLogModel from "../../shared/Model/TransactionLogModel";
import TransactionVTUModel from "../../shared/Model/TransactionVTUModel";

import { Op } from "sequelize";

type createwTw20 = {
  amount: number;
  charges: number;
  userId: string;
  initiatingWalletId: string;
  transferRecipientId: string;
  anchorPaymentId: string;
  anchorPaymentRef: string;
  status: string;
  bankAccountName: string;
  bankAccountNumber: string;
  bankName: string;
};

type tTransferRecipient = {
  recipientId: string;
  bankCode: string;
  bankName: string;
  bankAccountName: string;
  bankAccountNumber: string;
};

type tLog = {
  transactionId: string;
  walletId: string;
  entryType: string;
  amount: number;
  transactionStatus: string;
  statement: string;
  paymentMethod: string;
  transactionType: string;
  walletBalance: number;
};

interface FetchParams {
  limit?: number;
  page?: number;
  transactionType?: string;
  entryType?: string;
  from?: string;
  to?: string;
}

interface HistoryResult {
  transactions: any[];
  limit: number;
  page: number;
  totalCount: number;
}

class TransactionRepository implements ITransactionRepository {
  async getTransferRecipient(accountNumber: string, bankCode: string): Promise<any> {
    const transferRecipient = await TransferRecipientModel.findOne({
      where: {
        bankAccountNumber: accountNumber,
        bankCode: bankCode
      }
    });
    return transferRecipient || null;
  }

  async createTransferRecipient(fields: tTransferRecipient): Promise<any> {
    const { bankCode, bankAccountNumber, bankAccountName, recipientId, bankName } = fields;

    const recipient = await TransferRecipientModel.create({
      recipientId: recipientId,
      bankCode: bankCode,
      bankName: bankName,
      bankAccountNumber: bankAccountNumber,
      bankAccountName: bankAccountName
    });

    return recipient;
  }

  async createTransactionWallet2Other(fields: createwTw20): Promise<any> {
    const {
      amount,
      charges,
      initiatingWalletId,
      transferRecipientId,
      status,
      userId,
      anchorPaymentId,
      anchorPaymentRef
    } = fields;

    const transactionData = await TransactionModel.create({
      transactionType: "WITHDRAW",
      amount: amount,
      charges: charges,
      status: status,
      initiatingWallet: initiatingWalletId,
      userId: userId
    });

    const tWallet2OtherData = await TransactionWallet2OtherModel.create({
      transactionId: transactionData.id,
      transferRecipientId: transferRecipientId,
      anchorPaymentId: anchorPaymentId,
      anchorPaymentRef: anchorPaymentRef,
      reason: "ok"
    });

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      charges: transactionData.charges,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      created_at: transactionData.created_at
    };
  }

  async writeLog(fields: tLog): Promise<void> {
    const {
      transactionId,
      walletId,
      entryType,
      amount,
      transactionStatus,
      statement,
      paymentMethod,
      transactionType,
      walletBalance
    } = fields;

    await TransactionLogModel.create({
      transactionId: transactionId,
      walletId: walletId,
      entryType: entryType,
      amount: amount,
      transactionType: transactionType,
      transactionStatus: transactionStatus,
      paymentMethod: paymentMethod,
      statement: statement,
      balance: walletBalance
    });
  }

  async getTransactionHistory(walletId: string, params: any): Promise<any> {
    const { limit = 20, page = 1, transactionType, entryType, from, to } = params;

    const where: any = { walletId };
    if (transactionType) where.transactionType = transactionType;
    if (entryType) where.entryType = entryType;

    if (from) {
      const start = from;
      const end = to ?? new Date().toISOString();
      where.created_at = { [Op.between]: [start, end] };
    }

    const totalCount = await TransactionLogModel.count({ where });
    const transactions = await TransactionLogModel.findAll({
      where,
      order: [["created_at", "DESC"]],
      limit,
      offset: (page - 1) * limit,
      raw: true
    });

    return {
      transactions,
      limit,
      page,
      totalCount
    };
  }

  async getTransactionType(transactionId: string): Promise<any> {
    const transaction = await TransactionModel.findOne({
      attributes: ["id", "transactionType"],
      where: { id: transactionId },
      raw: true
    });
    if (!transaction) return null;

    return transaction.transactionType;
  }

  async getTransactionWallet2Other(transactionId: string): Promise<any> {
    const transactionData: any = await TransactionModel.findOne({ where: { id: transactionId } });
    const transactionW2OData = await TransactionWallet2OtherModel.findOne({
      where: { transactionId: transactionId }
    });
    const recipient = await TransferRecipientModel.findOne({
      where: { id: transactionW2OData?.transferRecipientId }
    });
    if (!transactionData || !transactionW2OData || !recipient) return null;

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      userId: transactionData.userId,
      reason: transactionW2OData.reason,
      created_at: transactionData.created_at,

      recipientId: transactionW2OData.transferRecipientId,
      paymentId: transactionW2OData.anchorPaymentId,
      paymentRef: transactionW2OData.anchorPaymentRef,

      bankAccountName: recipient.bankAccountName,
      bankAccountNumber: recipient.bankAccountNumber,
      bankName: recipient.bankName
    };
  }

  async getTransactionDeposit(transactionId: string): Promise<any> {
    const transactionData: any = await TransactionModel.findOne({ where: { id: transactionId } });
    const transactionWDData = await TransactionWalletDepositModel.findOne({
      where: { transactionId: transactionId }
    });
    if (!transactionData || !transactionWDData) return null;

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      userId: transactionData.userId,
      created_at: transactionData.created_at,

      depositType: transactionWDData.depositType,
      virtualNubanId: transactionWDData.virtualNubanId,
      paymentId: transactionWDData.anchorPaymentId,
      paymentRef: transactionWDData.anchorPaymentRef,

      accountName: transactionWDData.accountName,
      accountNumber: transactionWDData.accountNumber,
      bankName: transactionWDData.bankName,
      reason: transactionWDData.reason
    };
  }

  async getTransaction(transactionId: string): Promise<any> {
    const transaction = await TransactionModel.findOne({ where: { id: transactionId }, raw: true });

    return transaction;
  }

  async createTransactionWallet2Wallet(fields: any): Promise<any> {
    const {
      transactionType,
      amount,
      charges,
      initiatingWalletId,
      receivingWallet,
      status,
      userId,
      anchorPaymentId,
      anchorPaymentRef,
      reason
    } = fields;

    const transactionData = await TransactionModel.create({
      transactionType: transactionType,
      amount: amount,
      charges: charges,
      status: status,
      initiatingWallet: initiatingWalletId,
      userId: userId
    });

    const transactionW2WData = await TransactionWallet2WalletModel.create({
      transactionId: transactionData.id,
      receivingWalletId: receivingWallet,
      anchorPaymentId: anchorPaymentId,
      anchorPaymentRef: anchorPaymentRef,
      reason: reason || ""
    });

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      charges: transactionData.charges,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      receivingWallet: transactionW2WData.receivingWalletId,
      created_at: transactionData.created_at
    };
  }

  async getTransactionWallet2Wallet(transactionId: string): Promise<any> {
    const transactionData: any = await TransactionModel.findOne({ where: { id: transactionId } });
    const transactionW2WData = await TransactionWallet2WalletModel.findOne({
      where: { transactionId: transactionId }
    });

    if (!transactionData || !transactionW2WData) return null;

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      userId: transactionData.userId,
      created_at: transactionData.created_at,

      receivingWalletId: transactionW2WData.receivingWalletId,
      anchorPaymentId: transactionW2WData.anchorPaymentId,
      anchorPaymentRef: transactionW2WData.anchorPaymentRef,
      reason: transactionW2WData.reason
    };
  }

  async getTransactionVTU(transactionId: string): Promise<any> {
    const transactionData: any = await TransactionModel.findOne({ where: { id: transactionId } });
    const transactionVTU = await TransactionVTUModel.findOne({
      where: { transactionId: transactionId }
    });

    if (!transactionData || !transactionVTU) return null;

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      userId: transactionData.userId,
      created_at: transactionData.created_at,

      plan: transactionVTU.plan,
      phoneNumber: transactionVTU.phoneNumber,
      provider: transactionVTU.provider,
      network: transactionVTU.network,
      reference: transactionVTU.reference
    };
  }
}

export default TransactionRepository;
