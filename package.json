{"name": "lendsqr_democredit", "version": "1.0", "engines": {"node": ">=8.12.0"}, "description": "", "repository": "", "main": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"db:init": "npx sequelize-cli db:migrate && npx sequelize-cli db:seed:all", "dev": "set NODE_ENV=development&& nodemon --exec npx ts-node ./index.ts", "test": "set NODE_ENV=test&& ts-mocha tests/unit/**/*test.ts --bail", "build": "tsc", "lint": "eslint .", "lint:write": "eslint --debug . --fix", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major", "push-tags": "git push --tags origin"}, "dependencies": {"@hapi/boom": "^10.0.0", "@sendgrid/mail": "^8.1.5", "@sentry/node": "5.10.2", "@types/bcrypt": "^5.0.0", "@types/bcryptjs": "^2.4.2", "@types/chai-as-promised": "^7.1.5", "@types/knex": "^0.16.1", "@types/multer": "^2.0.0", "@types/uuid": "^9.0.1", "app-root-path": "^2.2.1", "axios": "^0.27.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.0", "body-parser-graphql": "^1.1.0", "config": "^2.0.1", "cookie-parser": "^1.4.5", "cors": "2.8.5", "debug": "^4.1.1", "dotenv": "^16.6.1", "ejs": "^3.1.8", "express": "4.17.1", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "helmet": "^3.22.0", "http-errors": "^1.7.3", "imagekit": "^6.0.0", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "knex": "^2.4.2", "mongoose": "^5.9.7", "morgan": "^1.10.0", "multer": "^2.0.2", "mysql2": "^2.3.3", "node-mailjet": "^3.3.1", "nodemailer": "^6.9.1", "npm": "^6.14.10", "path": "^0.12.7", "sequelize": "^6.31.1", "snyk": "^1.972.0", "twilio": "^5.7.1", "url": "0.11.0", "uuid": "^8.3.2", "winston": "^3.2.1"}, "devDependencies": {"@types/app-root-path": "^1.2.5", "@types/body-parser": "^1.19.2", "@types/chai": "^4.3.5", "@types/config": "^3.3.0", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.1", "@types/morgan": "^1.9.4", "@types/sinon": "^10.0.15", "@types/winston": "^2.4.4", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "chai-http": "^4.3.0", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.2.0", "eslint-config-prettier": "^3.6.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-mocha": "^5.3.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-security": "^1.4.0", "mocha": "^10.0.0", "nodemon": "1.18.3", "prettier": "^1.19.1", "sequelize-cli": "^6.4.1", "sinon": "^14.0.0", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^5.8.3"}, "snyk": true}