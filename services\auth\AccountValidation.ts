import Joi from "joi";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
const PHONE_FORMAT_REGEXP = /^\+234\d{10}$/;
const DOB_FORMAT_REGEXP = /^(0[0-9]|1[0-9]|2[0-9]|3[0-1])-(0[0-9]|1[0-2])-\d{4}$/;
const BVN_FORMAT_REGEXP = /^\d{11}$/;

class AccountValidation {
  private static loginAccountSchema = Joi.object({
    email: Joi.string()
      .email({ minDomainSegments: 2, tlds: { allow: false } })
      .required(),
    password: Joi.string().required()
  });

  private static createAccountSchema = Joi.object({
    firstname: Joi.string().required(),
    lastname: Joi.string().required(),
    email: Joi.string()
      .email({ minDomainSegments: 2, tlds: { allow: false } })
      .required(),
    phone: Joi.string()
      .regex(PHONE_FORMAT_REGEXP)
      .required()
      .messages({
        "string.pattern.base": "invalid phone format"
      }),
    gender: Joi.string().required(),
    dob: Joi.string()
      .regex(DOB_FORMAT_REGEXP)
      .required()
      .messages({
        "string.pattern.base": "date-of-birth does not match pattern dd-mm-yyyy"
      }),
    bvn: Joi.string()
      .regex(BVN_FORMAT_REGEXP)
      .required()
      .messages({
        "string.pattern.base": "invalid bvn input"
      }),
    password: Joi.string().required()
  });

  private static requestPhoneVerificationSchema = Joi.object({
    phone: Joi.string()
      .regex(PHONE_FORMAT_REGEXP)
      .required()
  });

  private static requestEmailVerificationSchema = Joi.object({
    email: Joi.string()
      .email({ minDomainSegments: 2, tlds: { allow: false } })
      .required()
  });

  private static passwordResetRequestSchema = Joi.object({
    id: Joi.string().required()
  });

  private static passwordResetSchema = Joi.object({
    password: Joi.string().required()
  });

  public static async createAccount(requestData: any): Promise<any | void> {
    const { firstname, lastname, email, phone, gender, dob, bvn, password } = requestData;
    try {
      return await AccountValidation.createAccountSchema.validateAsync({
        firstname,
        lastname,
        email,
        phone,
        gender,
        dob,
        bvn,
        password
      });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }

  public static async loginAccount(requestData: any): Promise<any | void> {
    const { email, password } = requestData;
    try {
      return await AccountValidation.loginAccountSchema.validateAsync({ email, password });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }

  public static async requestEmailVerification(requestData: any): Promise<any | void> {
    const { email } = requestData;
    try {
      return await AccountValidation.requestEmailVerificationSchema.validateAsync({ email });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }

  public static async requestPhoneVerification(requestData: any): Promise<any | void> {
    const { phone } = requestData;
    try {
      return await AccountValidation.requestPhoneVerificationSchema.validateAsync({ phone });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }

  public static async passwordResetRequest(requestData: any): Promise<any | void> {
    const { id } = requestData;
    try {
      return await AccountValidation.passwordResetRequestSchema.validateAsync({ id });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }

  public static async passwordReset(requestData: any): Promise<any | void> {
    const { password } = requestData;
    try {
      return await AccountValidation.passwordResetSchema.validateAsync({ password });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }
}

export default AccountValidation;
