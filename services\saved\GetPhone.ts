import IPhoneRepository from "./PhoneRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class GetPhone {
  private _repository: IPhoneRepository;

  constructor(phoneRepository: IPhoneRepository) {
    this._repository = phoneRepository;
  }

  async init(userId: string, alias: string): Promise<any> {
    const phone = await this._repository.getPhone(userId, alias);
    if (!phone) throw new AppException(domainError.NOT_FOUND, "no phone found");

    return { phone };
  }
}

export default GetPhone;
