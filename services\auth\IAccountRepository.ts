import Account from "./Account";

interface IAccountRepository {
  emailExists(email: string): Promise<boolean>;
  phoneExists(email: string): Promise<boolean>;
  bvnExists(bvn: string): Promise<boolean>;
  createAccount(
    firstname: string,
    lastname: string,
    gender: string,
    dob: string,
    email: string,
    phone: string,
    password: string
  ): Promise<Account>;

  getAccountByEmail(email: string): Promise<Account | null>;

  saveDeviceInfo(
    userId: string,
    deviceId: string,
    deviceOS: string,
    deviceToken: string
  ): Promise<void>;
}

export default IAccountRepository;
