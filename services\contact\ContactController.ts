import { Request, Response, NextFunction } from "express";
import Mailer from "../../mailer";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class ContactController {
  public static async submitContact(req: Request, res: Response, next: NextFunction) {
    try {
      const { firstName, lastName, email, phoneNumber, message } = req.body;

      if (!firstName || !lastName || !email || !phoneNumber || !message) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "firstName, lastName, email, phoneNumber, and message are required"
        );
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "Invalid email format"
        );
      }

      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "Invalid phone number format"
        );
      }

      if (message.trim().length < 10) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "Message must be at least 10 characters long"
        );
      }

      if (message.trim().length > 2000) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "Message must be less than 2000 characters"
        );
      }

      await sendSupportEmail(firstName, lastName, email, phoneNumber, message);

      await sendUserConfirmationEmail(firstName, email);

      const response = {
        success: true,
        message: "Support message sent successfully",
        data: {
          firstName,
          lastName,
          email,
          phoneNumber,
          messageSentAt: new Date().toISOString(),
          supportEmail: "<EMAIL>"
        }
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }


}

async function sendSupportEmail(firstName: string, lastName: string, email: string, phoneNumber: string, message: string) {
  const subject = `New Support Message from ${firstName} ${lastName}`;
  const body = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c3e50;">AgentPesa Support</h1>
      </div>

      <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">New Support Message</h2>

        <div style="background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #2c3e50; margin-top: 0;">Contact Information:</h3>
          <p style="margin: 5px 0;"><strong>Name:</strong> ${firstName} ${lastName}</p>
          <p style="margin: 5px 0;"><strong>Email:</strong> ${email}</p>
          <p style="margin: 5px 0;"><strong>Phone:</strong> ${phoneNumber}</p>
          <p style="margin: 5px 0;"><strong>Date:</strong> ${new Date().toLocaleString()}</p>
        </div>

        <div style="background-color: #ffffff; padding: 20px; border-radius: 5px;">
          <h3 style="color: #2c3e50; margin-top: 0;">Message:</h3>
          <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #2980b9; border-radius: 3px;">
            <p style="margin: 0; white-space: pre-wrap; line-height: 1.6;">${message}</p>
          </div>
        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 5px;">
          <p style="margin: 0; color: #2980b9; font-size: 14px;">
            <strong>Action Required:</strong> Please respond to this support request within 24 hours.
          </p>
        </div>
      </div>
    </div>
  `;

  await Mailer.send("<EMAIL>", subject, body);
}

async function sendUserConfirmationEmail(firstName: string, email: string) {
  const subject = "Your message has been received - AgentPesa Support";
  const body = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c3e50;">AgentPesa</h1>
      </div>

      <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">Thank you for contacting us, ${firstName}!</h2>

        <p style="color: #555; font-size: 16px; line-height: 1.6;">
          We have received your support message and our team is reviewing it now.
        </p>

        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <p style="color: #2980b9; font-weight: bold; margin: 0;">
            � Message Received
          </p>
          <p style="color: #2980b9; margin: 10px 0 0 0;">
            Your message has been forwarded to our support <NAME_EMAIL>
          </p>
        </div>

        <p style="color: #555; font-size: 16px; line-height: 1.6;">
          <strong>What happens next?</strong><br>
          • Our support team will review your message<br>
          • You can expect a response within 24-48 hours<br>
          • We'll reply directly to the email address you provided
        </p>

        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="color: #856404; margin: 0; font-size: 14px;">
            <strong>Need urgent assistance?</strong> For critical issues, please call our support hotline.
          </p>
        </div>
      </div>

      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #888; font-size: 14px;">
          Best regards,<br>
          <strong>The AgentPesa Support Team</strong>
        </p>
      </div>
    </div>
  `;

  await Mailer.send(email, subject, body);
}

export default ContactController;
