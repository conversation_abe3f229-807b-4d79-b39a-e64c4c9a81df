export interface AirtimeRequest {
  phoneNumber: string;
  amount: number;
  network: string;
  userId: string;
}

export interface AirtimeResponse {
  success: boolean;
  message: string;
  data?: {
    reference: string;
    status: string;
    amount: number;
    phoneNumber: string;
    network: string;
    transactionId?: string;
    created_at: string;
    wallet?: {
      id: string;
      balance: string;
    };
  };
  error?: string;
}

export interface NetworkProvider {
  code: string;
  name: string;
  serviceId: string;
}

export interface AnchorAirtimeRequest {
  data: {
    type: "Airtime";
    attributes: {
      phoneNumber: string;
      amount: number;
      reference: string;
      provider: string;
    };
    relationships: {
      account: {
        data: {
          type: "DepositAccount";
          id: string;
        };
      };
    };
  };
}

export interface AnchorAirtimeResponse {
  status: boolean;
  message: string;
  data?: {
    amount: number;
    phone: string;
    status: string;
    ident: string;
    api_response: string;
  };
}
