import VTUTransaction from "../models/airtime.Model";
import TransactionModel from "../../shared/Model/TransactionModel";
import TransactionVTUModel from "../../shared/Model/TransactionVTUModel";
import TransactionLogModel from "../../shared/Model/TransactionLogModel";
import { VTU_CONFIG } from "../config/vtu.config";

export class VTURepository {
  async createTransaction(data: {
    transactionType: string;
    amount: number;
    status: string;
    initiatingWalletId: string;
    userId: string;
    phoneNumber: string;
    network: string;
    provider: string;
    reference: string;
    providerReference?: string;
    anchorResponse?: string;
    plan?: string;
  }) {
    const transaction = await TransactionModel.create;

    const transactionData = await TransactionModel.create({
      transactionType: data.transactionType,
      amount: data.amount,
      status: data.status,
      initiatingWallet: data.initiatingWalletId,
      userId: data.userId
    });

    const transactionVTU = await TransactionVTUModel.create({
      transactionId: transactionData.id,
      phoneNumber: data.phoneNumber,
      network: data.network,
      provider: data.provider,
      reference: data.reference,
      providerReference: data.providerReference,
      anchorResponse: data.anchorResponse,
      plan: data.plan
    });

    return {
      id: transactionData.id,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      status: transactionData.status,
      initiatingWallet: transactionData.initiatingWallet,
      phoneNumber: transactionVTU.phoneNumber,
      network: transactionVTU.network,
      provider: transactionVTU.provider,
      plan: transactionVTU.plan,
      created_at: transactionData.created_at
    };
  }
}
