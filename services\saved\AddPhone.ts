import IPhoneRepository from "./PhoneRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import TransferRecipientModel from "../../shared/Model/TransferRecipientModel";

class AddPhone {
  private _repository: IPhoneRepository;

  constructor(phoneRepository: IPhoneRepository) {
    this._repository = phoneRepository;
  }

  async init(userId: string, phoneNumber: string, alias: string): Promise<any> {
    const aliasExists = !!(await this._repository.getPhone(userId, alias));
    if (aliasExists) throw new AppException(domainError.TEST_ERROR, `phone ${alias} exists`);

    const phone: any = await this._repository.addPhone(userId, {
      phoneNumber: phoneNumber,
      alias: alias
    });

    return { phone };
  }
}

export default AddPhone;
