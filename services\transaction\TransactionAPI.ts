import { Router, Request, Response, NextFunction } from "express";
import TransactionController from "./TransactionController";
import jwt from "../../libraries/jwt";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";

const router: Router = Router();

router.post("/withdraw", AuthProtectionMiddleware, TransactionController.transactionWallet2Other);
router.post("/transfer", AuthProtectionMiddleware, TransactionController.transactionWallet2Wallet);
router.get("/", AuthProtectionMiddleware, TransactionController.getTransactionHistory);
router.get("/:id", AuthProtectionMiddleware, TransactionController.getTransaction);

export default router;
