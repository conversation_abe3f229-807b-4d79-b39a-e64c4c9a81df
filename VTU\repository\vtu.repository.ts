import VTUTransaction from '../models/airtime.Model'; 
import { VTU_CONFIG } from '../config/vtu.config'; 

export class VTURepository {
  
  async createTransaction(data: {
    userId: string;
    transactionId?: string;
    serviceType: string;
    phoneNumber: string;
    amount: number;
    network: string;
    reference?: string;
    status?: string;
  }) {
    try {
      return await VTUTransaction.create({
        ...data,
        provider: 'ANCHOR',
        status: data.status || VTU_CONFIG.TRANSACTION_STATUS.PENDING
      });
    } catch (error: any) {
      console.error('Error creating VTU transaction:', error.message);
      throw new Error('Failed to create VTU transaction');
    }
  }

  async getTransactionById(id: string) {
    try {
      return await VTUTransaction.findByPk(id);
    } catch (error: any) {
      console.error('Error getting VTU transaction:', error.message);
      throw new Error('Failed to get VTU transaction');
    }
  }

  async getTransactionByReference(reference: string) {
    try {
      return await VTUTransaction.findOne({
        where: { reference }
      });
    } catch (error: any) {
      console.error('Error getting VTU transaction by reference:', error.message);
      throw new Error('Failed to get VTU transaction');
    }
  }

  async getUserTransactions(userId: string, limit: number = 50) {
    try {
      return await VTUTransaction.findAll({
        where: { userId },
        order: [['created_at', 'DESC']],
        limit
      });
    } catch (error: any) {
      console.error('Error getting user VTU transactions:', error.message);
      throw new Error('Failed to get user transactions');
    }
  }

  async updateTransaction(id: string, data: {
    status?: string;
    providerReference?: string;
    anchorResponse?: string;
  }) {
    try {
      const transaction = await VTUTransaction.findByPk(id);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      return await transaction.update(data);
    } catch (error: any) {
      console.error('Error updating VTU transaction:', error.message);
      throw new Error('Failed to update VTU transaction');
    }
  }

  async getTransactionsByStatus(status: string, limit: number = 100) {
    try {
      return await VTUTransaction.findAll({
        where: { status },
        order: [['created_at', 'ASC']],
        limit
      });
    } catch (error: any) {
      console.error('Error getting transactions by status:', error.message);
      throw new Error('Failed to get transactions');
    }
  }
}