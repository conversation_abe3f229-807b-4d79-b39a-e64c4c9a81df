import IBeneficiaryRepository from "./IBeneficiaryRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import TransferRecipientModel from "../../shared/Model/TransferRecipientModel";

class AddBeneficiary {
  private _repository: IBeneficiaryRepository;

  constructor(beneficiaryRepository: IBeneficiaryRepository) {
    this._repository = beneficiaryRepository;
  }

  async init(
    userId: string,
    accountName: string,
    accountNumber: string,
    bankCode: string,
    bankName: string,
    alias: string
  ): Promise<any> {
    console.log(bankCode);
    console.log(accountNumber);

    const aliasExists = !!(await this._repository.getBeneficiary(userId, alias));
    if (aliasExists) throw new AppException(domainError.TEST_ERROR, `beneficiary ${alias} exists`);

    const transferRecipient = await TransferRecipientModel.findOne({
      where: {
        bankCode: bankCode,
        bankAccountNumber: accountNumber
      },
      raw: true
    });

    if (!transferRecipient)
      throw new AppException(domainError.TEST_ERROR, "error saving beneficiary");

    const beneficiary: any = await this._repository.addBeneficiary(userId, {
      accountName: transferRecipient.bankAccountName,
      accountNumber: accountNumber,
      bankCode: bankCode,
      bankName: transferRecipient.bankName,
      alias: alias
    });

    return { beneficiary };
  }
}

export default AddBeneficiary;
