import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransferRecipient extends Model<
  InferAttributes<TransferRecipient>,
  InferCreationAttributes<TransferRecipient>
> {
  declare id: CreationOptional<string>;
  declare recipientId: string;

  declare bankCode: string;
  declare bankName: string;
  declare bankAccountName: string;
  declare bankAccountNumber: string;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

TransferRecipient.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },

    recipientId: {
      field: "recipient_id",
      type: DataTypes.STRING
    },

    bankCode: {
      field: "bank_code",
      type: DataTypes.STRING
    },

    bankName: {
      field: "bank_name",
      type: DataTypes.STRING
    },

    bankAccountName: {
      field: "bank_account_name",
      type: DataTypes.STRING
    },

    bankAccountNumber: {
      field: "bank_account_number",
      type: DataTypes.STRING
    },

    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "transfer_recipient",
    tableName: "transfer_recipient",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default TransferRecipient;
