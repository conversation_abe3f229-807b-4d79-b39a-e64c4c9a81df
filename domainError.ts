// Request Errors: wrong request, parameters, request body, missing or invalid headers => 1001+
// Authentication Errors: Verifiying an identity, authorizing an action => 1101+
// Session Errors: You as a logged in entity, access, permissions, restrictions you bear(as defined in you session) => 1201+

// Application Error
// Other Errors: => 1501+

export type TypeDomainError = {
  errorCode: number | string;
  statusCode: number | string;
  message: string;
};

export const domainError = {
  TEST_ERROR: {
    errorCode: 1001,
    statusCode: 400,
    message: "you hit test error"
  },
  NOT_FOUND: {
    errorCode: 1001,
    statusCode: 400,
    message: "resource not found"
  },

  INVALID_OR_MISSING_PARAMETER: {
    errorCode: 1002,
    statusCode: 400,
    message: "invalid or missing fields"
  },

  INVALID_OR_MISSING_HEADER: {
    errorCode: 1003,
    statusCode: 401,
    message: "invalid or missing header"
  },

  AUTHORIZATION_ERROR: {
    errorCode: 1201,
    statusCode: 401,
    message: "unauthorized"
  },

  // Authentication Errors
  INVALID_CREDENTIALS: {
    errorCode: 1101,
    statusCode: 400,
    message: "invalid credentials"
  },

  INVALID_BEARER_TOKEN: {
    errorCode: 1102,
    statusCode: 401,
    message: "invalid bearer token"
  },

  INVALID_OTP: {
    errorCode: 1103,
    statusCode: 401,
    message: "invalid or Expired OTP"
  },

  INVALID_OR_EXPIRED_VERIFICATION_TOKEN: {
    errorCode: 1103,
    statusCode: 401,
    message: "invalid or Expired Verification Token"
  },

  UNAVAILABLE_EMAIL_ADDRESS: {
    errorCode: 1201,
    statusCode: 400,
    message: "email is not available"
  },

  UNAVAILABLE_PHONE: {
    errorCode: 1202,
    statusCode: 400,
    message: "phone is not available"
  },

  UNAVAILABLE_ACCOUNT: {
    errorCode: 1203,
    statusCode: 400,
    message: "account not available"
  },

  /* domain errors 
    1500 account verification/setup
    1600 Wallet
  */
  BVN_ALREADY_VERIFIED: {
    errorCode: 1500,
    statusCode: 400,
    message: "BVN is already verified for this account"
  },

  WALLET_AUTHORIZATION_ERROR: {
    errorCode: 1601,
    statusCode: 400,
    message: "Cannot process wallet request at the moment"
  },

  WALLET_NOT_FOUND: {
    errorCode: 1604,
    statusCode: 400,
    message: "Cannot process wallet request at the moment"
  }
};
