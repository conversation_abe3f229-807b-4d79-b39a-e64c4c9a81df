import Jo<PERSON> from 'joi';
import { VTU_CONFIG } from '../config/vtu.config';

export class DataValidation {

  static validateDataRequest(data: any) {
    const schema = Joi.object({
      phoneNumber: Joi.string()
        .pattern(/^(\+234|234|0)[789][01]\d{8}$/)
        .required()
        .messages({
          'string.pattern.base': 'Please provide a valid Nigerian phone number',
          'any.required': 'Phone number is required'
        }),
      dataCode: Joi.string()
        .required()
        .messages({
          'any.required': 'Data code is required'
        }),
      network: Joi.string()
        .valid(...Object.keys(VTU_CONFIG.ANCHOR.NETWORKS))
        .optional()
        .messages({
          'any.only': 'Invalid network provider'
        }),
      userId: Joi.string()
        .uuid()
        .required()
        .messages({
          'string.uuid': 'Invalid user ID format',
          'any.required': 'User ID is required'
        })
    });

    return schema.validate(data);
  }
}
