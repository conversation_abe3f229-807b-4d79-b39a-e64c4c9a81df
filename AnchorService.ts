import { error } from "winston";

const ANCHOR_URL = "";
const ANCHOR_API_KEY: any = process.env.ANCHOR_API_KEY;

type UserDataObject = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
};
type AddressDataObject = {
  country: string;
  state: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  postalCode: string;
};
type IdLevel2DataObject = {
  dateOfBirth: string;
  gender: string;
  bvn: string;
  selfieImage: string;
};

async function CreateCustomer(userData: UserDataObject, addressData: AddressDataObject) {
  try {
    let url = "https://api.sandbox.getanchor.co/api/v1/customers";

    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: {
            fullName: {
              firstName: userData.firstName,
              lastName: userData.lastName
            },
            address: {
              country: addressData.country,
              state: addressData.state,
              addressLine_1: addressData.addressLine1,
              addressLine_2: addressData.addressLine2,
              city: addressData.city,
              postalCode: addressData.postalCode
            },
            email: userData.email,
            phoneNumber: userData.phoneNumber
          },
          type: "IndividualCustomer"
        }
      })
    };

    console.log("CALLING CreateCustomer...");
    console.log(options.body);
    const res = await fetch(url, options);

    if (res.status === 202) return {};
    if (res.status >= 400) throw res;

    const data = await res.json();
    return data;
  } catch (err) {
    console.error(error);
    HandleError(err);
  }
}

async function SearchCustomer(email: string) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/customers/search?customerType=IndividualCustomer&searchValue=${email}`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      }
    };

    console.log("CALLING SearchCustomer...");
    const res = await fetch(url, options);

    if (res.status === 202) return {};
    if (res.status >= 400) throw res;

    const data = await res.json();
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function CreateDepositAccount(customerId: string) {
  try {
    const url = "https://api.sandbox.getanchor.co/api/v1/accounts";

    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: { productName: "SAVINGS" },
          relationships: {
            customer: {
              data: {
                type: "IndividualCustomer",
                id: customerId
              }
            }
          },
          type: "DepositAccount"
        }
      })
    };

    console.log("CALLING CreateDepositAccount");
    console.log(options.body);
    const res = await fetch(url, options);
    const data = await res.json();
    console.log(data);
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function FetchDepositAccounts(customerId: string) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/accounts?customerId=${customerId}`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key":
          "nK7cV.fc29bb3b17b67742ebd6f6d7eea1ba213630b9268b9edd7f8e231c4c4f3489e176d6632dc889a1a63374f81b8ed96eb5cbea"
      }
    };

    console.log("CALLING SearchCustomer...");
    const res = await fetch(url, options);

    if (res.status === 202) return {};
    if (res.status >= 400) throw res;

    const data = await res.json();
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function VerifyKYCLevel2(customerId: string, idLevel2Data: IdLevel2DataObject) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/customers/${customerId}/verification/individual`;

    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: {
            level: "TIER_2",
            level2: {
              bvn: idLevel2Data.bvn,
              selfie: idLevel2Data.selfieImage,
              dateOfBirth: idLevel2Data.dateOfBirth,
              gender: idLevel2Data.gender
            }
          },
          type: "Verification"
        }
      })
    };

    console.log("CALLING VerifyKYCLevel2...");
    console.log(options.body);
    const res = await fetch(url, options);
    console.log("INNER", res);

    if (res.status === 202) return {};
    if (res.status >= 400) throw res;

    const data = await res.json();
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function GetVirtualNuban(vnId: string) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/virtual-nubans/${vnId}`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      }
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));

    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function LisVirtualNubans(depositAccountId: string) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/virtual-nubans?settlementAccountId=${depositAccountId}`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key":
          "nK7cV.fc29bb3b17b67742ebd6f6d7eea1ba213630b9268b9edd7f8e231c4c4f3489e176d6632dc889a1a63374f81b8ed96eb5cbea"
      }
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));

    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function VerifyAccountDetails(bankCode: string, accountNumber: string) {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/payments/verify-account/${bankCode}/${accountNumber}`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      }
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function ListBanks() {
  try {
    const url = `https://api.sandbox.getanchor.co/api/v1/banks`;
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      }
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function createCounterparty(accountName: string, accountNumber: string, bankCode: string) {
  try {
    const url = "https://api.sandbox.getanchor.co/api/v1/counterparties";
    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: {
            accountName: accountName,
            accountNumber: accountNumber,
            bankCode: bankCode
          },
          type: "CounterParty"
        }
      })
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
    /*
    {
  "data": {
    "id": "**************-anc_cp",
    "type": "CounterParty",
    "attributes": {
      "createdAt": "2025-05-26T08:15:19",
      "bank": {
        "id": "****************-anc_bk",
        "name": "OPAY",
        "nipCode": "100004"
      },
      "accountName": "AYODELE OLUWANIYI BENJAMIN",
      "accountNumber": "**********",
      "updatedAt": "2025-05-26T08:15:19",
      "status": "ACTIVE"
    }
  }
}
  */
  } catch (err) {
    HandleError(err);
  }
}

async function initiateNIPTransfer(
  amount: number,
  depositAccountId: string,
  counterPartyId: string,
  reason: string = "ok"
) {
  try {
    const url = "https://api.sandbox.getanchor.co/api/v1/transfers";
    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: { currency: "NGN", amount: amount * 100, reason: reason },
          relationships: {
            destinationAccount: { data: { type: "SubAccount" } },
            account: {
              data: { type: "DepositAccount", id: depositAccountId }
            },
            counterParty: {
              data: { type: "CounterParty", id: counterPartyId }
            }
          },
          type: "NIPTransfer"
        }
      })
    };

    console.log("CALLING initiateNIPTransfer...");
    console.log(options.body);
    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function initiateBookTransfer(
  amount: number,
  senderDepositAccountId: string,
  receiverDepositAccountId: string,
  reason: string = "ok"
) {
  try {
    const url = "https://api.sandbox.getanchor.co/api/v1/transfers";
    const options = {
      method: "POST",
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      },
      body: JSON.stringify({
        data: {
          attributes: {
            currency: "NGN",
            amount: amount,
            reason: "reason"
          },
          relationships: {
            destinationAccount: {
              data: {
                type: "DepositAccount",
                id: receiverDepositAccountId
              }
            },
            account: {
              data: {
                type: "DepositAccount",
                id: senderDepositAccountId
              }
            }
          },
          type: "BookTransfer"
        }
      })
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
  } catch (err) {
    HandleError(err);
  }
}

async function foo() {
  try {
    const url =
      "https://api.sandbox.getanchor.co/api/v1/payments/verify-account/1111001/**********";
    const options = {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-anchor-key": ANCHOR_API_KEY
      }
    };

    const res = await fetch(url, options);
    const data = await res.json();
    console.log(JSON.stringify(data));
    return data;
  } catch (err) {
    throw err;
  }
}

function HandleError(error: any) {
  const errorStatus = error.status;
  const errorTag = TagError(error.status);
  console.log(
    "ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR"
  );
  console.log(`${errorStatus} -- ${errorTag}`);
  console.log(error);
  console.log(
    "ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR ERROR"
  );
}

function TagError(status: number) {
  if (status === 400) return "USER";
  if (status === 401) return "SYSTEM";
  if (status >= 500) return "SERVER";
  else return "UNDEFINED";
}

export default {
  CreateCustomer,
  SearchCustomer,
  CreateDepositAccount,
  FetchDepositAccounts,
  VerifyKYCLevel2,
  GetVirtualNuban,
  LisVirtualNubans,
  VerifyAccountDetails,
  createCounterparty,
  initiateNIPTransfer,
  initiateBookTransfer,
  ListBanks
};
