import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class User extends Model<InferAttributes<User>, InferCreationAttributes<User>> {
  declare id: CreationOptional<string>;
  declare userId: string;
  declare balance: CreationOptional<number>;
  declare pin: CreationOptional<string>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

User.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    balance: {
      field: "balance",
      type: DataTypes.NUMBER,
      defaultValue: 0
    },
    pin: {
      field: "pin",
      type: DataTypes.STRING
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "wallet",
    tableName: "wallet",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default User;
