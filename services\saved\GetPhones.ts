import IPhoneRepository from "./IPhoneRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class GetPhones {
  private _repository: IPhoneRepository;

  constructor(phoneRepository: IPhoneRepository) {
    this._repository = phoneRepository;
  }

  async init(userId: string): Promise<any> {
    const phones = await this._repository.getPhones(userId);
    return { phones };
  }
}

export default GetPhones;
