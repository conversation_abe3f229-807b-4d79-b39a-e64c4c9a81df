import AppException from "../../AppException";
import { domainError } from "../../domainError";
import IWalletRepository from "./IWalletRepository";
import Wallet from "./Wallet";

type WalletViewReturnValue = {
  wallet: Wallet;
};

class WalletView {
  private _repository: IWalletRepository;

  constructor(walletRepository: IWalletRepository) {
    this._repository = walletRepository;
  }

  async init(wallet_id: string): Promise<WalletViewReturnValue> {
    const wallet: Wallet | null = await this._repository.getWallet(wallet_id);
    if (!wallet) throw new AppException(domainError.WALLET_AUTHORIZATION_ERROR);

    return { wallet };
  }

  async getUserWallet(userId: string) {
    const wallet: Wallet | null = await this._repository.getUserWallet(userId);
    if (!wallet) return null;

    return { wallet };
  }
}

export default WalletView;
