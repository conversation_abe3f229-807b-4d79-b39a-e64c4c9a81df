import ITransactionRepository from "./ITransactionRepository";
import IWalletService from "../wallet/IWalletService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class TransactionView {
  private transactionId: string;

  private _repository: ITransactionRepository;
  private _walletService: IWalletService;

  constructor(transactionRepository: ITransactionRepository, walletService: IWalletService) {
    this._repository = transactionRepository;
    this._walletService = walletService;
  }

  setTransactionId(transactionId: string) {
    this.transactionId = transactionId;
  }

  async init(): Promise<any> {
    let transaction;

    const transactionType = await this._repository.getTransactionType(this.transactionId);
    if (!transactionType)
      throw new AppException(
        domainError.NOT_FOUND,
        `transaction with id ${this.transactionId} not found`
      );

    console.log(transactionType);

    if (transactionType === "WITHDRAW")
      transaction = await this._repository.getTransactionWallet2Other(this.transactionId);
    else if (transactionType === "DEPOSIT")
      transaction = await this._repository.getTransactionDeposit(this.transactionId);
    else if (transactionType === "TRANSFER")
      transaction = await this._repository.getTransactionWallet2Wallet(this.transactionId);
    else if (transactionType === "AIRTIME" || transactionType === "DATA")
      transaction = await this._repository.getTransactionVTU(this.transactionId);

    return transaction;
  }
}

export default TransactionView;
