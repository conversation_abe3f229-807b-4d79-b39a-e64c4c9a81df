import { config } from "dotenv";
config();

import Mailer from "./mailer";

async function testIcons8Email() {
  const firstName = "Test";
  const email = "<EMAIL>"; // Your email for testing
  
  const subject = "✅ Icons8 Test - Professional Social Media Icons";
  const body = `
   <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icons8 Test Email</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        table, td {
            border-collapse: collapse;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td style="padding: 40px 20px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 600px; max-width: 600px; background-color: #ffffff; border-radius: 8px; padding: 40px;">
                    
                    <tr>
                        <td align="center">
                            <h1 style="margin: 0 0 20px 0; color: #1f2937; font-size: 28px;">
                                🎉 Icons8 Test - Professional Icons!
                            </h1>
                            <p style="margin: 0 0 30px 0; color: #4b5563; font-size: 16px;">
                                Testing the new professional social media icons from Icons8
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td align="center">
                            <p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">
                                Follow us on social media:
                            </p>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                <tr>
                                    <!-- Facebook -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.facebook.com/61577778871615/posts/122121339824925962/?mibextid=rS40aB7S9Ucbxw6v" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #1877f2; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                            <img src="https://img.icons8.com/color/48/facebook-new.png" alt="Facebook" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- X (Twitter) -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://x.com/agentpesa/status/1953764937261556068?t=U7zYPPac6SewLQhObQr6qQ&s=19" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #000000; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                            <img src="https://img.icons8.com/color/48/twitterx--v2.png" alt="Twitter" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- Instagram -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.instagram.com/p/DNFzKrpCT-c/?igsh=NTc4MTIwNjQ2YQ==" target="_blank" style="display: inline-block; width: 32px; height: 32px; background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                            <img src="https://img.icons8.com/color/48/instagram-new--v1.png" alt="Instagram" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- LinkedIn -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.linkedin.com/posts/agentpesa_agentpesa-weekendfun-digitalpayments-activity-7359532138024595456-DOyQ?utm_source=share&utm_medium=member_desktop&rcm=ACoAACviJ0YBTXuW25FMgX-il9nsczv7hW8S72Q" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #0077b5; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                            <img src="https://img.icons8.com/color/48/linkedin.png" alt="LinkedIn" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <tr>
                        <td align="center" style="padding-top: 30px;">
                            <p style="margin: 0; color: #6b7280; font-size: 14px;">
                                ✅ If you can see all 4 social media icons clearly, the fix worked!<br>
                                📧 Icons should be colorful and professional-looking
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `;

  try {
    console.log("🚀 Sending Icons8 test email...");
    await Mailer.send(email, subject, body);
    console.log("✅ Icons8 test email sent successfully!");
    console.log("📧 Check your email to verify the professional icons display correctly");
    console.log("🎯 You should see 4 colorful, professional social media icons");
  } catch (error) {
    console.error("❌ Error sending test email:", error);
  }
}

// Run the test
testIcons8Email();
