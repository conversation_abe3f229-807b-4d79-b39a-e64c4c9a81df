import { config } from "dotenv";
config();

import Mailer from "./mailer";

async function testIcons8Email() {
  const firstName = "Test";
  const email = "<EMAIL>"; // Your email for testing
  
  const subject = "🎨 Material Filled Icons - AgentPesa Brand Colors";
  const body = `
   <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material Filled Icons Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        table, td {
            border-collapse: collapse;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td style="padding: 40px 20px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 600px; max-width: 600px; background-color: #ffffff; border-radius: 8px; padding: 40px;">
                    
                    <tr>
                        <td align="center">
                            <h1 style="margin: 0 0 20px 0; color: #1f2937; font-size: 28px;">
                                � Material Filled Icons - AgentPesa Style!
                            </h1>
                            <p style="margin: 0 0 30px 0; color: #4b5563; font-size: 16px;">
                                Testing the new Material Filled icons with AgentPesa brand colors
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td align="center">
                            <p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">
                                Follow us on social media:
                            </p>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                <tr>
                                    <!-- Facebook -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.facebook.com/61577778871615/posts/122121339824925962/?mibextid=rS40aB7S9Ucbxw6v" target="_blank" style="display: inline-block; width: 40px; height: 40px; background-color: #2563eb; border-radius: 8px; text-align: center; line-height: 40px; text-decoration: none; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);">
                                            <img src="https://img.icons8.com/material-rounded/24/ffffff/facebook-new.png" alt="Facebook" width="24" height="24" style="display: block; margin: 8px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- X (Twitter) -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://x.com/agentpesa/status/1953764937261556068?t=U7zYPPac6SewLQhObQr6qQ&s=19" target="_blank" style="display: inline-block; width: 40px; height: 40px; background-color: #1e40af; border-radius: 8px; text-align: center; line-height: 40px; text-decoration: none; box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);">
                                            <img src="https://img.icons8.com/material-rounded/24/ffffff/twitter.png" alt="Twitter" width="24" height="24" style="display: block; margin: 8px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- Instagram -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.instagram.com/p/DNFzKrpCT-c/?igsh=NTc4MTIwNjQ2YQ==" target="_blank" style="display: inline-block; width: 40px; height: 40px; background-color: #3730a3; border-radius: 8px; text-align: center; line-height: 40px; text-decoration: none; box-shadow: 0 2px 4px rgba(55, 48, 163, 0.2);">
                                            <img src="https://img.icons8.com/material-rounded/24/ffffff/instagram-new.png" alt="Instagram" width="24" height="24" style="display: block; margin: 8px auto; border: 0;">
                                        </a>
                                    </td>
                                    <!-- LinkedIn -->
                                    <td style="padding: 0 8px;">
                                        <a href="https://www.linkedin.com/posts/agentpesa_agentpesa-weekendfun-digitalpayments-activity-7359532138024595456-DOyQ?utm_source=share&utm_medium=member_desktop&rcm=ACoAACviJ0YBTXuW25FMgX-il9nsczv7hW8S72Q" target="_blank" style="display: inline-block; width: 40px; height: 40px; background-color: #1d4ed8; border-radius: 8px; text-align: center; line-height: 40px; text-decoration: none; box-shadow: 0 2px 4px rgba(29, 78, 216, 0.2);">
                                            <img src="https://img.icons8.com/material-rounded/24/ffffff/linkedin.png" alt="LinkedIn" width="24" height="24" style="display: block; margin: 8px auto; border: 0;">
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <tr>
                        <td align="center" style="padding-top: 30px;">
                            <p style="margin: 0; color: #6b7280; font-size: 14px;">
                                ✅ If you can see 4 Material Filled icons with blue gradients, the fix worked!<br>
                                🎨 Icons should have consistent AgentPesa brand colors with subtle shadows
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `;

  try {
    console.log("🚀 Sending Material Filled icons test email...");
    await Mailer.send(email, subject, body);
    console.log("✅ Material Filled icons email sent successfully!");
    console.log("📧 Check your email to verify the new icons display correctly");
    console.log("� You should see 4 Material Filled icons with AgentPesa brand colors and subtle shadows");
  } catch (error) {
    console.error("❌ Error sending test email:", error);
  }
}

// Run the test
testIcons8Email();
