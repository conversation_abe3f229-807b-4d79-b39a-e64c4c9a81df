import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class UserKYC extends Model<InferAttributes<UserKYC>, InferCreationAttributes<UserKYC>> {
  declare id: CreationOptional<string>;
  declare userId: string;

  declare bvn: CreationOptional<string>;
  declare bvnVerified: CreationOptional<boolean>;
  declare bvnMessage: CreationOptional<string>;

  declare bvnPhone: CreationOptional<string>;
  declare phoneVerified: CreationOptional<boolean>;
  declare phoneMessage: CreationOptional<string>;

  declare selfieImage: CreationOptional<string>;
  declare selfieVerified: CreationOptional<boolean>;
  declare selfieMessage: CreationOptional<string>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

UserKYC.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },

    bvn: {
      field: "bvn",
      type: DataTypes.STRING
    },
    bvnVerified: {
      field: "bvn_verified",
      type: DataTypes.STRING
    },
    bvnMessage: {
      field: "bvn_message",
      type: DataTypes.STRING
    },
    bvnPhone: {
      field: "bvn_phone",
      type: DataTypes.STRING
    },
    phoneVerified: {
      field: "phone_verified",
      type: DataTypes.STRING
    },
    phoneMessage: {
      field: "phone_message",
      type: DataTypes.STRING
    },
    selfieImage: {
      field: "selfie_image",
      type: DataTypes.STRING
    },
    selfieVerified: {
      field: "selfie_verified",
      type: DataTypes.STRING
    },
    selfieMessage: {
      field: "selfie_message",
      type: DataTypes.STRING
    },

    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user_kyc",
    tableName: "user_kyc",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default UserKYC;
