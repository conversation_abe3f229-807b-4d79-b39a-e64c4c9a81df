import IBeneficiaryRepository from "./IBeneficiaryRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class GetBeneficiary {
  private _repository: IBeneficiaryRepository;

  constructor(beneficiaryRepository: IBeneficiaryRepository) {
    this._repository = beneficiaryRepository;
  }

  async init(userId: string): Promise<any> {
    const beneficiaries = await this._repository.getBeneficiaries(userId);
    console.log(beneficiaries);

    return { beneficiaries };
  }
}

export default GetBeneficiary;
