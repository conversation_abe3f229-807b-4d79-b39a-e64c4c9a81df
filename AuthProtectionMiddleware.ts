import { Request, Response, NextFunction } from "express";
import AppException from "./AppException";
import { domainError } from "./domainError";
import jwt from "./libraries/jwt";
import Usermodel from "./shared/Model/UserModel";
import SessionModel from "./shared/Model/SessionModel";

async function AuthProtectionMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    let bearerToken: any =
      req.headers.authorization && req.headers.authorization.split("Bearer ")[1];

    if (!bearerToken)
      throw new AppException(domainError.INVALID_OR_MISSING_HEADER, "missing bearer token");

    const tokenData: any = await jwt.decode(bearerToken);
    if (!tokenData)
      throw new AppException(domainError.INVALID_OR_MISSING_HEADER, "invalid or expired token");

    if (tokenData.issued_at + tokenData.expire_in < new Date().getTime())
      throw new AppException(domainError.INVALID_OR_MISSING_HEADER, "invalid or expired token");

    const userSession = await SessionModel.findOne({ where: { userId: tokenData.data.id } });
    if (!userSession)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );
    if (userSession.sessionToken !== bearerToken)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );

    let authenticated_user: any = tokenData;
    const user = await Usermodel.findByPk(authenticated_user.data.userId);
    if (!user)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );
    if (user.deactivated)
      throw new AppException(
        domainError.INVALID_OR_MISSING_HEADER,
        "could not authenticate you at the moment. please sign in again"
      );

    res.locals.authenticated_user = authenticated_user.data;

    next();
  } catch (err) {
    next(err);
  }
}

export default AuthProtectionMiddleware;
