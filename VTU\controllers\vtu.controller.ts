import { Request, Response } from 'express';
import { VTUService } from '../service/main.service'; 
import { VTU_CONFIG } from '../config/vtu.config';
import { VTUValidation } from '../validations/vtu.validation';
import { AnchorVTUService } from '../service/anchor.service'; 

export class VTUController {
  private vtuService: VTUService;

  constructor() {
    this.vtuService = new VTUService();
  }

  async purchaseAirtime(req: Request, res: Response) {
    try {
      const { phoneNumber, amount, network } = req.body;
      const { userId } = res.locals.authenticated_user;

      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          message: 'Phone number is required'
        });
      }

      if (!amount) {
        return res.status(400).json({
          success: false,
          message: 'Amount is required'
        });
      }

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const phoneRegex = /^(\+234|234|0)[789][01]\d{8}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid Nigerian phone number (e.g., +2348012345678 or 08012345678)'
        });
      }

      const numericAmount = Number(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Amount must be a positive number'
        });
      }

      if (numericAmount < 50) {
        return res.status(400).json({
          success: false,
          message: 'Minimum airtime amount is ₦50'
        });
      }

      if (numericAmount > 10000) {
        return res.status(400).json({
          success: false,
          message: 'Maximum airtime amount is ₦10,000'
        });
      }

      if (network && !['MTN', 'GLO', 'AIRTEL', '9MOBILE'].includes(network.toUpperCase())) {
        return res.status(400).json({
          success: false,
          message: 'Network must be one of: MTN, GLO, AIRTEL, 9MOBILE'
        });
      }
      if (typeof userId !== 'string' || userId.trim().length === 0) {
        return res.status(401).json({
          success: false,
          message: 'Invalid user authentication'
        });
      }

      const result = await this.vtuService.purchaseAirtime({
        phoneNumber,
        amount: Number(amount),
        network,
        userId
      });

      const statusCode = result.success ? 200 : 400;
      
      return res.status(statusCode).json(result);

    } catch (error: any) {
      console.error('Purchase Airtime Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getAirtimeBillers(req: Request, res: Response) {
    try {
      const anchorService = new AnchorVTUService();
      const result = await anchorService.getAirtimeBillers();
      
      return res.status(200).json(result);

    } catch (error: any) {
      console.error('Get Billers Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getTransactionStatus(req: Request, res: Response) {
    try {
      const { reference } = req.params;

      if (!reference) {
        return res.status(400).json({
          success: false,
          message: 'Transaction reference is required'
        });
      }

      const result = await this.vtuService.getTransactionStatus(reference);
      
      const statusCode = result.success ? 200 : 404;
      
      return res.status(statusCode).json(result);

    } catch (error: any) {
      console.error('Get Transaction Status Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getUserTransactions(req: Request, res: Response) {
    try {
      const { userId } = res.locals.authenticated_user;
      const limit = parseInt(req.query.limit as string) || 50;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const result = await this.vtuService.getUserTransactions(userId, limit);
      
      return res.status(200).json(result);

    } catch (error: any) {
      console.error('Get User Transactions Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getNetworkProviders(req: Request, res: Response) {
    try {
      const networks = Object.entries(VTU_CONFIG.ANCHOR.NETWORKS).map(([code, config]) => ({
        code,
        name: config.name,
        serviceId: config.serviceId
      }));

      return res.status(200).json({
        success: true,
        message: 'Network providers retrieved successfully',
        data: networks
      });

    } catch (error: any) {
      console.error('Get Network Providers Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async detectNetwork(req: Request, res: Response) {
    try {
      const { phoneNumber } = req.body;

      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          message: 'Phone number is required'
        });
      }

      const validation = VTUValidation.validatePhoneNumber(phoneNumber);
      
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: validation.error || 'Invalid phone number'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Network detected successfully',
        data: {
          phoneNumber: validation.formatted,
          network: validation.network,
          networkName: VTU_CONFIG.ANCHOR.NETWORKS[validation.network as keyof typeof VTU_CONFIG.ANCHOR.NETWORKS]?.name
        }
      });

    } catch (error: any) {
      console.error('Detect Network Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}