import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransactionFund extends Model<
  InferAttributes<TransactionFund>,
  InferCreationAttributes<TransactionFund>
> {
  declare transactionId: string;
  declare depositType: string;
  declare virtualNubanId: string;
  declare anchorPaymentId: string;
  declare anchorPaymentRef: string;
  declare paidAt: string;

  declare accountNumber: string;
  declare accountName: string;
  declare bankName: string;
  declare reason: CreationOptional<string>;
}

TransactionFund.init(
  {
    transactionId: {
      field: "transaction_id",
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true
    },
    depositType: {
      field: "deposit_type",
      type: DataTypes.STRING
    },
    virtualNubanId: {
      field: "virtual_nuban_id",
      type: DataTypes.STRING
    },
    anchorPaymentId: {
      field: "anchor_payment_id",
      type: DataTypes.STRING
    },
    anchorPaymentRef: {
      field: "anchor_payment_ref",
      type: DataTypes.STRING
    },
    paidAt: {
      field: "paid_at",
      type: DataTypes.STRING
    },
    accountNumber: {
      field: "account_number",
      type: DataTypes.STRING
    },
    accountName: {
      field: "account_name",
      type: DataTypes.STRING
    },
    bankName: {
      field: "bank_name",
      type: DataTypes.STRING
    },
    reason: {
      field: "reason",
      type: DataTypes.STRING,
      defaultValue: ""
    }
  },
  {
    sequelize,
    modelName: "transaction_wallet_deposit",
    tableName: "transaction_wallet_deposit",
    createdAt: false,
    updatedAt: false
  }
);

export default TransactionFund;
