import IPhoneRepository from "./IPhoneRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import TransferRecipientModel from "../../shared/Model/TransferRecipientModel";

class UpdatePhone {
  private _repository: IPhoneRepository;

  constructor(phoneRepository: IPhoneRepository) {
    this._repository = phoneRepository;
  }

  async init(aliasId: string, userId: string, phoneNumber: string, alias: string): Promise<any> {
    let isOldAlias = aliasId === alias;

    const aliasExists = !!(await this._repository.getPhone(userId, aliasId));
    if (!aliasExists)
      throw new AppException(domainError.TEST_ERROR, `phone ${aliasId} does not exist`);

    if (!isOldAlias) {
      const aliasExists = !!(await this._repository.getPhone(userId, alias));
      if (aliasExists) throw new AppException(domainError.TEST_ERROR, `error saving alias`);
    }

    const phone: any = await this._repository.updatePhone(userId, aliasId, {
      phoneNumber: phoneNumber,
      alias: alias
    });

    return { phone };
  }
}

export default UpdatePhone;
