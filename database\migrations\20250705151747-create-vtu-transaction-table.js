'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vtu_transaction', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      transaction_id: {
        type: Sequelize.STRING(36),
        allowNull: true
        // Remove the foreign key reference to transaction table
      },
      service_type: {
        type: Sequelize.STRING,
        allowNull: false
      },
      phone_number: {
        type: Sequelize.STRING,
        allowNull: false
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      provider: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'ANCHOR'
      },
      network: {
        type: Sequelize.STRING,
        allowNull: false
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'PENDING'
      },
      reference: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true // Add unique constraint
      },
      provider_reference: {
        type: Sequelize.STRING,
        allowNull: true
      },
      anchor_response: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('vtu_transaction', ['user_id']);
    await queryInterface.addIndex('vtu_transaction', ['reference']);
    await queryInterface.addIndex('vtu_transaction', ['status']);
    await queryInterface.addIndex('vtu_transaction', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vtu_transaction');
  }
};