import IPhoneRepository from "./PhoneRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class RemovePhone {
  private _repository: IPhoneRepository;

  constructor(phoneRepository: IPhoneRepository) {
    this._repository = phoneRepository;
  }

  async init(userId: string, alias: string): Promise<any> {
    await this._repository.removePhone(userId, alias);
  }
}

export default RemovePhone;
