import AppException from "../../AppException";
import { domainError } from "../../domainError";
import OTP from "./otp/OTPGenerate";
import Bcrypt from "../../libraries/bcrypt";
import JWT from "../../libraries/jwt";
import Mailer from "../../mailer";
import SMSER from "../../smser";
import AccountRepository from "./AccountRepository";

const accountRepository = new AccountRepository();

let ERR_MESSAGE_PHONE;
let ERR_MESSAGE_EMAIL;

class AccountPasswordReset {
  static async RequestOTPVerification(accountId: string) {
    let account = await accountRepository.getAccountByEmail(accountId);

    if (!account)
      throw new AppException(
        domainError.UNAVAILABLE_EMAIL_ADDRESS,
        "provided email not associated with any account"
      );

    const otp: any = await OTP.generate({ accountId: account.id });
    await _mailOTP(otp.otpCode, accountId);

    return { otpId: otp.otpId, otpCode: otp.otpCode };
  }

  static async ConfirmOTPVerification(otp_id: string, otp_code: string) {
    const otp: any = await OTP.retrieve(otp_id);
    if (!otp) throw new AppException(domainError.INVALID_OTP);
    if (otp.otp_code !== otp_code) throw new AppException(domainError.INVALID_OTP);

    if (new Date() > new Date(otp.generated_time + otp.expiration_time))
      throw new AppException(domainError.INVALID_OTP);

    const token: string = await JWT.sign({
      accountId: otp.data.accountId,
      otpId: otp_id,
      data: null,
      generated_time: new Date().getTime(),
      expiration_time: 300000
    });

    return { otpId: otp.otp_id, token: token };
  }

  static async newPassword(otpToken: string, password: string) {
    const otpTokenData = await _verifyOTPToken(otpToken);
    const { accountId } = otpTokenData;

    let account = await accountRepository.getAccountById(accountId);
    if (!account)
      throw new AppException(
        domainError.UNAVAILABLE_ACCOUNT,
        "could not update your password at this time"
      );

    const hashedPassword = await Bcrypt.hash(password, 10);
    await accountRepository.updateAccountPassword(account.id, hashedPassword);

    return true;
  }
}

async function _verifyOTPToken(otpToken: string) {
  if (!otpToken)
    throw new AppException(
      domainError.INVALID_OR_EXPIRED_VERIFICATION_TOKEN,
      "error verifiying action process"
    );

  const otpTokenData: any = await JWT.decode(otpToken);
  if (!otpTokenData)
    throw new AppException(
      domainError.INVALID_OR_EXPIRED_VERIFICATION_TOKEN,
      "error verifiying action process"
    );

  if (new Date() > new Date(otpTokenData.generated_time + otpTokenData.expiration_time))
    throw new AppException(
      domainError.INVALID_OR_EXPIRED_VERIFICATION_TOKEN,
      "error verifiying action process"
    );

  return otpTokenData;
}

function _detectIdType(id: string) {
  const PHONE_REGEXP = /^[\+][0-9]{6,15}$/;

  if (!!id.match(PHONE_REGEXP)) return "TYPE_PHONE";
  else return "TYPE_EMAIL";
}

async function _mailOTP(code: string, email: string) {
  const OTPMailContent = {
    subject: "AgentPesa: Password Reset OTP",
    body: `<div class="email-container">
          <h2>${code}</h2>
          <p>Use the above code to verify your account password reset</p>
          <p>This code is valid for 10 minutes. If you did not request this code, please ignore this email.</p>
          <div class="footer">
              <p>Thank you,</p>
              <p>AgentPesa</p>
          </div>
      </div>`
  };

  await Mailer.send(email, OTPMailContent["subject"], OTPMailContent["body"]);
}

async function _smsOTP(code: string, phone: string) {
  const OTPMailContent = {
    body: `AgentPesa: ${code} is your password reset verification code and is valid for 10 minutes. If you did not request this code, please ignore this message. Thank you.`
  };

  await SMSER.send(phone, OTPMailContent["body"]);
}

export default AccountPasswordReset;
