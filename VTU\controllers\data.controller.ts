import { Request, Response } from 'express';
import { DataService } from '../service/data.service';
import { AnchorVTUService } from '../service/anchor.service';

export class DataController {
  private dataService: DataService;

  constructor() {
    this.dataService = new DataService();
  }
  
  async purchaseData(req: Request, res: Response) {
    try {
      const { phoneNumber, dataCode } = req.body;
      const { userId } = res.locals.authenticated_user;

      if (!phoneNumber) {
        return res.status(400).json({
          success: false,
          message: 'Phone number is required'
        });
      }

      if (!dataCode) {
        return res.status(400).json({
          success: false,
          message: 'Data code is required'
        });
      }

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const phoneRegex = /^(\+234|234|0)[789][01]\d{8}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid Nigerian phone number (e.g., +2348012345678 or 08012345678)'
        });
      }

      if (typeof dataCode !== 'string' || dataCode.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Data code must be a valid string'
        });
      }

      if (dataCode.length < 2 || dataCode.length > 50) {
        return res.status(400).json({
          success: false,
          message: 'Data code must be between 2 and 50 characters'
        });
      }

      if (typeof userId !== 'string' || userId.trim().length === 0) {
        return res.status(401).json({
          success: false,
          message: 'Invalid user authentication'
        });
      }

      const result = await this.dataService.purchaseData({
        phoneNumber,
        dataCode,
        userId
      });

      const statusCode = result.success ? 200 : 400;
      
      return res.status(statusCode).json(result);

    } catch (error: any) {
      console.error('Purchase Data Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getDataPlans(req: Request, res: Response) {
    try {
      const { network } = req.query;

      const result = await this.dataService.getDataPlans(network as string);
      
      return res.status(200).json(result);

    } catch (error: any) {
      console.error('Get Data Plans Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  async getDataBillers(req: Request, res: Response) {
    try {
      const anchorService = new AnchorVTUService();
      const result = await anchorService.getDataBillers();
      
      return res.status(200).json(result);

    } catch (error: any) {
      console.error('Get Data Billers Controller Error:', error.message);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}