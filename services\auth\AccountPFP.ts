import AppException from "../../AppException";
import { domainError } from "../../domainError";
import Usermodel from "../../shared/Model/UserModel";
import UserPFPModel from "../../shared/Model/UserPFPModel";
import ImageKit from "imagekit";

class AccountPFP {
  static imagekitErrandBoy = new ImageKit({
    publicKey: process.env.IMAGEKIT_PUBLIC_KEY || "",
    privateKey: process.env.IMAGEKIT_PRIVATE_KEY || "",
    urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT || ""
  });

  private static async upload(file: any) {
    try {
      const remoteFolder = "agentpesa/pfp";
      const fileName = file["originalname"];
      const fileData = file.buffer;
      return await AccountPFP.imagekitErrandBoy.upload({
        file: fileData,
        fileName: fileName,
        folder: "agentpesa/pfp"
      });
    } catch (e) {
      console.error(e);
      throw new AppException(domainError.TEST_ERROR, "error uploading file");
    }
  }

  private static async deleteFile(fileId: string) {
    try {
      console.log(fileId);
      const data = await AccountPFP.imagekitErrandBoy.deleteFile(fileId);
      console.log(data);
    } catch (e) {
      console.error(e);
      throw new AppException(domainError.TEST_ERROR, "error uploading file");
    }
  }

  public static async init(userId: string, file: any) {
    const acceptableFormats = ["jpeg", "jpg", "png"];
    const sizeLimit = 1000 * 1000 * 10;

    let fileName = file.originalname;
    let fileNameArray = fileName.split(".");
    let fileExtension = fileNameArray[fileNameArray.length - 1];

    if (!acceptableFormats.includes(fileExtension.toLowerCase()))
      throw new AppException(domainError.TEST_ERROR, "unsupported file type");
    if (file.size > sizeLimit) throw new AppException(domainError.TEST_ERROR, "file too large");

    const uploadDetails: any = await AccountPFP.upload(file);
    let userpfp = await UserPFPModel.findOne({ where: { userId: userId } });

    if (userpfp) await AccountPFP.deleteFile(userpfp.fileId);
    if (userpfp) {
      await UserPFPModel.update(
        {
          fileId: uploadDetails.fileId,
          name: uploadDetails.name,
          url: uploadDetails.url,
          thumbnailUrl: uploadDetails.thumbnailUrl,
          fileType: uploadDetails.fileType,
          fileExtension: uploadDetails.fileExtension
        },
        { where: { userId: userId } }
      );
    } else {
      await UserPFPModel.create({
        userId: userId,
        fileId: uploadDetails.fileId,
        name: uploadDetails.name,
        url: uploadDetails.url,
        thumbnailUrl: uploadDetails.thumbnailUrl,
        fileType: uploadDetails.fileType,
        fileExtension: uploadDetails.fileExtension
      });
    }

    await Usermodel.update({ pfp: uploadDetails.url }, { where: { id: userId } });
    userpfp = await UserPFPModel.findOne({ where: { userId: userId }, raw: true });
    return { userpfp: userpfp };
  }

  public static async getPfP(userId: string) {
    const userpfp = await UserPFPModel.findOne({ where: { userId: userId }, raw: true });
    return { userpfp: userpfp };
  }
}

export default AccountPFP;
