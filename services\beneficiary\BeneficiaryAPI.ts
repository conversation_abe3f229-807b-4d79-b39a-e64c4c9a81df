import { Router, Request, Response, NextFunction } from "express";
import BeneficiaryController from "./BeneficiaryController";
import jwt from "../../libraries/jwt";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";

const router: Router = Router();

router.post("/", AuthProtectionMiddleware, BeneficiaryController.addBeneficiary);
router.get("/", AuthProtectionMiddleware, BeneficiaryController.getBeneficiaries);
router.get("/:alias", AuthProtectionMiddleware, BeneficiaryController.getBeneficiary);
router.put("/:alias", AuthProtectionMiddleware, BeneficiaryController.updateBeneficiary);
router.delete("/:alias", AuthProtectionMiddleware, BeneficiaryController.deleteBeneficiary);

export default router;
