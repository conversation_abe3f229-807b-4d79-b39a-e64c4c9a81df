import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class User extends Model<InferAttributes<User>, InferCreationAttributes<User>> {
  declare id: CreationOptional<string>;
  declare email: string;
  declare phone: string;

  declare firstname: string;
  declare lastname: string;
  declare gender: string;
  declare dob: string;
  declare pfp: CreationOptional<string>;

  declare password: string;

  declare deviceId: CreationOptional<string>;
  declare deviceOS: CreationOptional<string>;
  declare deviceToken: CreationOptional<string>;

  declare deactivated: CreationOptional<boolean>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

User.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    firstname: {
      type: DataTypes.STRING
    },
    lastname: {
      type: DataTypes.STRING
    },
    gender: {
      type: DataTypes.STRING
    },
    dob: {
      type: DataTypes.STRING
    },
    pfp: {
      type: DataTypes.STRING
    },
    email: {
      type: DataTypes.STRING
    },
    phone: {
      type: DataTypes.STRING
    },
    password: {
      type: DataTypes.STRING
    },
    deviceId: {
      field: "device_id",
      type: DataTypes.STRING
    },
    deviceOS: {
      field: "device_os",
      type: DataTypes.STRING
    },
    deviceToken: {
      field: "device_token",
      type: DataTypes.STRING
    },
    deactivated: {
      field: "deactivated",
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user",
    tableName: "user",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default User;
