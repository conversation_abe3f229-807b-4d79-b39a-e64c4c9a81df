import AppException from "../../AppException";
import { domainError } from "../../domainError";
import Usermodel from "../../shared/Model/UserModel";
import bcrypt from "../../libraries/bcrypt";
import AccountLogin from "./AccountLogin";
import jwt from "../../libraries/jwt";
import AnchorService from "../../AnchorService";
import AnchorModel from "../../shared/Model/AnchorModel";
import AnchorVNModel from "../../shared/Model/AnchorVNModel";

import WalletService from "../../services/wallet/WalletService";

async function init(userId: string) {
  const walletService = new WalletService();

  const account = await Usermodel.findByPk(userId);
  if (!account) throw new AppException(domainError.NOT_FOUND, "user does not exist");
  if (account.deactivated) throw new AppException(domainError.TEST_ERROR, "deactivated account");

  let anchor;
  let virtualNuban: any;
  let vn: any;
  let isKYCVerified: any;

  const wallet: any = (await walletService.getUserWallet(userId)).wallet;
  anchor = (await AnchorModel.findOne({ where: { userId: userId } })) || {};
  vn = anchor?.defaultVirtualNuban
    ? await AnchorVNModel.findOne({
        where: { virtualNubanId: anchor.defaultVirtualNuban },
        attributes: ["bankName", "bankNipCode", "accountName", "accountNumber"]
      })
    : {};
  virtualNuban = vn || {};
  isKYCVerified = anchor.status === "approved";

  delete virtualNuban?.userId;
  delete virtualNuban?.id;
  delete virtualNuban?.bankId;

  return {
    account: account,
    wallet: wallet,
    anchor: anchor,
    virtualNuban,
    isKYCVerified: isKYCVerified
  };
}

export default init;
