#!/usr/bin/env node

"use strict";

const http = require("http");
const config = require("config");
const logger = require("../app/utils/logger");
const app = require("../app/app");

/**
 * Normalize a port into a number, string, or false.
 */
function normalizePort(val) {
  const value = parseInt(val, 10);

  if (Number.isNaN(value)) {
    // named pipe
    return val;
  }

  if (value >= 0) {
    // port number
    return value;
  }

  return false;
}

/**
 * Get port from environment and store in Express.
 */
const port = normalizePort(process.env.PORT || config.get("app.port"));
app.set("port", port);

/**
 * Create HTTP server.
 */
const server = http.createServer(app);

/**
 * Initialize Apollo server subscriptions
 */

/**
 * Event listener for HTTP server "error" event.
 */
function onError(error) {
  if (error.syscall !== "listen") {
    throw error;
  }

  const bind = typeof port === "string" ? `Pipe ${port}` : `Port ${port}`;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case "EACCES":
      logger.error(`${bind} requires elevated privileges`);
      // eslint-disable-next-line no-process-exit
      process.exit(1);
      // eslint-disable-next-line no-unreachable
      break;
    case "EADDRINUSE":
      logger.error(`${bind} is already in use`);
      // eslint-disable-next-line no-process-exit
      process.exit(1);
      // eslint-disable-next-line no-unreachable
      break;
    default:
      throw error;
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */
function onListening() {
  const addr = server.address();
  const bind = typeof addr === "string" ? `pipe ${addr}` : `pipe ${addr.port}`;
  logger.info(`Listening on ${bind} in ${app.get("env")} environment`);
}

/**
 * Listen on provided port, on all network interfaces.
 */
server.listen(port);
server.on("error", onError);
server.on("listening", onListening);
