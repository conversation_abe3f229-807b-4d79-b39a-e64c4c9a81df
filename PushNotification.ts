import firebaseAdmin from "./firebaseInitialize";

async function push(
  firebaseMessagingToken: string,
  data: {
    type: string;
    title: string;
    body: string;
    icon: string;
    notificationId: string;
  }
) {
  try {
    var notificationMessage = {
      token: firebaseMessagingToken,
      notification: {
        title: data.title,
        body: data.body
      },
      data: {
        type: data.type,
        notificationId: data.notificationId
      }
    };

    await firebaseAdmin.messaging().send(notificationMessage);
  } catch (error) {
    console.log(error);
  }
}

export default push;
