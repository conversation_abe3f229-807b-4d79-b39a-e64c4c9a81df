import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class TransactionLog extends Model<
  InferAttributes<TransactionLog>,
  InferCreationAttributes<TransactionLog>
> {
  declare id: CreationOptional<number>;
  declare transactionId: string;
  declare walletId: string;
  declare entryType: string;
  declare amount: number;
  declare transactionType: String;
  declare transactionStatus: String;
  declare paymentMethod: String;
  declare statement: string;
  declare balance: number;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

TransactionLog.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    transactionId: {
      field: "transaction_id",
      type: DataTypes.STRING
    },
    walletId: {
      field: "wallet_id",
      type: DataTypes.STRING
    },
    entryType: {
      field: "entry_type",
      type: DataTypes.STRING
    },
    amount: {
      field: "amount",
      type: DataTypes.NUMBER
    },
    transactionType: {
      field: "transaction_type",
      type: DataTypes.STRING
    },
    transactionStatus: {
      field: "transaction_status",
      type: DataTypes.STRING
    },
    paymentMethod: {
      field: "payment_method",
      type: DataTypes.STRING
    },
    statement: {
      field: "statement",
      type: DataTypes.STRING,
      defaultValue: ""
    },
    balance: {
      field: "balance",
      type: DataTypes.NUMBER
    },
    created_at: {
      field: "created_at",
      type: DataTypes.DATE
    },
    updated_at: {
      field: "updated_at",
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "transaction_log",
    tableName: "transaction_log",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default TransactionLog;
