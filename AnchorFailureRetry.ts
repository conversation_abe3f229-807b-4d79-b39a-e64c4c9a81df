import AppException from "./AppException";
import { domainError } from "./domainError";

import AnchorModel from "./shared/Model/AnchorModel";
import AnchorVNModel from "./shared/Model/AnchorVNModel";
import UserModel from "./shared/Model/UserModel";
import UserKYCModel from "./shared/Model/UserKYCModel";

import AnchorService from "./AnchorService";
import WalletService from "./services/wallet/WalletService";

import JWT from "./libraries/jwt";
import bcrypt from "./libraries/bcrypt";
import IWalletService from "./services/wallet/IWalletService";

import { Op } from "sequelize";
import generateRandomNigerianAddress from "./addressgenerator";

async function RetryAnchorFailures() {
  // no customer : create customer, verifykyc
  // customer, kyc failed, : do nothing
  // customer, kyc passed, no account : do something
  console.log("######### RetryAnchorFailures ##########");

  const anchors_no_customer = await AnchorModel.findAll({
    where: {
      [Op.or]: [{ customerId: null }, { customerId: "" }]
    } as any,
    raw: true
  });

  console.log("anchors_no_customer", anchors_no_customer);
  anchors_no_customer.forEach(anchor => NoCustomer(anchor.userId));

  const anchors_no_accounts = await AnchorModel.findAll({
    where: {
      [Op.and]: [
        {
          [Op.or]: [
            { depositAccountId: { [Op.is]: null } },
            { defaultVirtualNuban: { [Op.is]: null } }
          ]
        },
        {
          [Op.and]: [
            { [Op.and]: [{ customerId: { [Op.not]: null } }, { customerId: { [Op.ne]: "" } }] },
            { status: "approved" }
          ]
        }
      ]
    } as any,
    raw: true
  });

  console.log("anchors_no_accounts", anchors_no_accounts);
  anchors_no_accounts.forEach(anchor => NoAccount(anchor.userId));
}

// no customer! no dpa no vn
async function NoCustomer(userId: string) {
  const customer = await f1(userId);
  if (!customer) return;
  await f2(userId);
}

async function NoAccount(userId: string) {
  setTimeout(() => f3(userId), 10000);
  setTimeout(() => f4(userId), 15000);
}

async function f1(userId: string) {
  const user = await UserModel.findByPk(userId, { raw: true });
  const anchor = await AnchorModel.findOne({ where: { userId: userId }, raw: true });

  if (!user || !anchor) {
    console.log(`user or anchor not found on accout setup retry userId ${userId}`); // critical
    return false;
  }

  if (!anchor.customerId) {
    console.log(`############  RESOLVE CUSTOMER ${user.email}  ##############`);
    let customer = (await AnchorService.SearchCustomer(user.email))?.data?.[0];
    console.log(customer);

    // if customer, update
    // if !customer, create
    if (customer?.id) {
      const customerId = customer.id;
      await AnchorModel.update(
        {
          customerId: customerId,
          dateOfBirth: user.dob,
          phoneNumber: user.phone,
          selfieImage: "bxxvxvxbvasbbxvxvx"
        },
        { where: { id: anchor.id } }
      );
      console.log("customer update", await AnchorModel.findByPk(anchor.id));
      return true;
    }

    if (!customer?.id) {
      // check
      let fPhoneNumber = user.phone.replace("+234", "0");
      let customer: any = await AnchorService.CreateCustomer(
        {
          firstName: user.firstname,
          lastName: user.lastname,
          email: user.email,
          phoneNumber: fPhoneNumber
        },
        generateRandomNigerianAddress()
      );

      if (!customer?.data?.id) {
        console.warn(`could not create customer for userId ${userId}`); // retry 3
        return false;
      }

      const customerId = customer.data.id;
      await AnchorModel.update(
        {
          customerId: customerId,
          dateOfBirth: user.dob,
          phoneNumber: user.phone,
          selfieImage: "bxxvxvxbvasbbxvxvx"
        },
        { where: { id: anchor.id } }
      );
      return true;
    }
  }
}

async function f2(userId: string) {
  const user = await UserModel.findByPk(userId, { raw: true });
  const anchor = await AnchorModel.findOne({ where: { userId: userId } });
  let userKYC = await UserKYCModel.findOne({ where: { userId: userId }, raw: true });

  if (!user || !anchor || !userKYC) {
    console.log(`missing user, anchor or kyc on accout setup retry userId ${userId}`); // critical
    return;
  }

  if (!anchor.customerId) {
    console.log(`user customerId not found on accout setup retry userId ${userId}`);
    return;
  }
  if (!user.dob || !user.gender || !userKYC.bvn) {
    // kyc failed
    return;
  }

  const customerId = anchor.customerId;
  const dobReformat = user.dob
    .split("-")
    .reverse()
    .join("-");

  const kycVerification = await AnchorService.VerifyKYCLevel2(customerId, {
    dateOfBirth: dobReformat,
    gender: user.gender,
    bvn: userKYC.bvn,
    selfieImage: "bxxvxvxbvasbbxvxvx"
  });
  console.log("##########-kycVerification-##########");
  console.log(kycVerification);

  if (!kycVerification?.data?.id) return;

  await AnchorModel.update(
    {
      bvn: userKYC.bvn,
      status: "approved",
      message: "KYC initiated successfully"
    },
    { where: { id: customerId } }
  );
}

// has customer has kyc! no dpa no vn
async function f3(userId: any) {
  const anchor = await AnchorModel.findOne({ where: { userId: userId }, raw: true });
  if (!anchor) {
    console.warn(`user anchor not found on f2 userId ${userId}`);
    return;
  }

  let customerId = anchor.customerId;

  if (!anchor.depositAccountId) {
    let depositAccount = (await AnchorService.FetchDepositAccounts(customerId))?.data?.[0];
    if (depositAccount) {
      await AnchorModel.update(
        { depositAccountId: depositAccount.id },
        { where: { id: anchor.id } }
      );
      return;
    }

    if (!depositAccount) {
      let depositAccount = await AnchorService.CreateDepositAccount(customerId);
      if (depositAccount?.data?.id) {
        await AnchorModel.update(
          { depositAccountId: depositAccount.data.id },
          { where: { id: anchor.id } }
        );
        return;
      }
      if (!depositAccount?.data?.id) {
        console.warn(`could not create depositAccount on f2 userId ${userId}`); // critical
        return;
      }
    }
  }
}

async function f4(userId: any) {
  const anchor = await AnchorModel.findOne({ where: { userId: userId }, raw: true });
  if (!anchor) {
    console.warn(`user anchor not found on f3 userId ${userId}`);
    return;
  }

  let customerId = anchor.customerId;
  let depositAccountId = anchor.depositAccountId;

  if (!depositAccountId) {
    console.warn(`no depositAccountId on f3 userId ${userId}`); // critical
    return;
  }

  if (!anchor.defaultVirtualNuban) {
    let virtualNuban = (await AnchorService.LisVirtualNubans(depositAccountId))?.data?.[0];
    console.log(virtualNuban);

    if (virtualNuban) {
      await AnchorModel.update(
        { defaultVirtualNuban: virtualNuban.id },
        { where: { id: anchor.id } }
      );
      return;
    }

    if (!virtualNuban) {
      // should always create automatically for deposit
      console.warn(`no autocreated virtualNuban on f3 userId ${userId}`); // critical
      return;
    }
  }
}

setInterval(() => RetryAnchorFailures(), 5000);
