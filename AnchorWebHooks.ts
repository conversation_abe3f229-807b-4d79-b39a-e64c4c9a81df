import AnchorService from "./AnchorService";

import AnchorModel from "./shared/Model/AnchorModel";
import AnchorVNModel from "./shared/Model/AnchorVNModel";
import TransactionWallet2OtherModel from "./shared/Model/TransactionWallet2OtherModel";
import TransactionWalletDepositModel from "./shared/Model/TransactionWalletDepositModel";
import TransactionWallet2WalletModel from "./shared/Model/TransactionWallet2WalletModel";
import TransactionModel from "./shared/Model/TransactionModel";
import TransactionLogModel from "./shared/Model/TransactionLogModel";
import TransactionSystemChargeModel from "./shared/Model/TransactionSystemChargeModel";
import SessionModel from "./shared/Model/SessionModel";
import Push from "./PushNotification";

import WalletService from "./services/wallet/WalletService";

async function paymentSettled(data: any) {
  console.log("################### PAYMENT SETTLED");
  console.log(data);
  console.log(data.attributes);
  console.log(data.attributes.payment);
  console.log("#########################################");

  // const customerId = data.relationships.customer.data.id;
  const virtualNubanId = data.attributes.payment.virtualNuban.accountId;

  const paymentId = data.attributes.payment.paymentId;
  const paymentReference = data.attributes.payment.paymentReference;
  const paymentType = data.attributes.payment.type;
  const currency = data.attributes.payment.currency;
  const amount = data.attributes.payment.amount / 100;
  const fee = data.attributes.payment.fee;
  const reason = data.attributes.payment.narration;

  const payment: any = data.attributes.payment;
  const virtualNuban: any = data.attributes.payment.virtualNuban;
  const counterParty: any = data.attributes.payment.counterParty;

  const CHARGE_PERCENTAGE: number = 0.5 / 100;
  const MAX_INFLOW_CHARGE: number = 200;

  let charges: number = Math.ceil(amount * CHARGE_PERCENTAGE); // 0.5 percent
  if (charges > MAX_INFLOW_CHARGE) charges = MAX_INFLOW_CHARGE;
  let creditBalance = amount - charges;

  // find credit virtual nuban
  const anchorVirtualNuban = await AnchorVNModel.findOne({ where: { virtualNubanId } });
  if (!anchorVirtualNuban) {
    console.error(
      `could not find anchorVirtualNuban on payment.settled event virtualNubanId ${virtualNubanId} eventId ${data.id}`
    );
    return;
  }

  // get nuban linked user
  const userId = anchorVirtualNuban.userId;

  const walletService = new WalletService();
  const wallet: any = (await walletService.getUserWallet(userId)).wallet;
  if (!wallet) {
    console.error(
      `could not find user wallet on payment.settled event ${userId} eventId ${data.id}`
    );
    return; // critical error
  }

  const initiatorWallet = (await walletService.deposit(wallet.id, creditBalance)).wallet;

  const transaction = await TransactionModel.create({
    transactionType: "DEPOSIT",
    amount: amount,
    charges: charges,
    userId: userId,
    initiatingWallet: wallet.id,
    status: "SUCCESS"
  });

  const tWalletDepositModel = await TransactionWalletDepositModel.create({
    transactionId: transaction.id,
    depositType: "BANK_TRANSFER",
    virtualNubanId: virtualNubanId,
    anchorPaymentId: paymentId,
    anchorPaymentRef: paymentReference,
    paidAt: "",
    accountNumber: counterParty.accountNumber,
    accountName: counterParty.accountName,
    bankName: counterParty.bank.name,
    reason: reason
  });
  console.log(transaction, tWalletDepositModel);

  let source = counterParty.accountName;
  let initiatorStatement = "Transfer deposit to wallet";

  const initiatorLog = await TransactionLogModel.create({
    transactionId: transaction.id,
    walletId: initiatorWallet.id,
    entryType: "CREDIT",
    amount: amount,
    transactionType: "DEPOSIT",
    transactionStatus: "SUCCESS",
    paymentMethod: "TRANSFER",
    statement: initiatorStatement,
    balance: initiatorWallet.balance
  });

  const userSession = await SessionModel.findOne({ where: { userId: userId } });
  if (userSession?.deviceToken) {
    console.log("SENDING PUSH NOTIFICATION...");

    const token = userSession?.deviceToken;
    const notificationBy = `${counterParty.accountName} ${counterParty.bank.name}`;
    const notificationBody = `${amount} received from ${notificationBy}`;

    Push(token, {
      type: "TRANSACTION::DEPOSIT",
      title: "Deposit",
      body: notificationBody,
      icon: "bell",
      notificationId: "abcdef12345"
    });
  }

  console.log(initiatorLog);
}

async function BookTransferSuccessful(data: any, retryCount: number = 0, waitCount: number = 5000) {
  console.log("################### BOOK TRANSFER SUCCESSFUL ###################");
  console.log(data);
  console.log("#########################################");

  const walletService = new WalletService();
  const anchorPaymentId = data.relationships.transfer.data.id;

  const tWallet2Wallet = await TransactionWallet2WalletModel.findOne({
    where: { anchorPaymentId: anchorPaymentId }
  });
  console.log(tWallet2Wallet);
  console.log(tWallet2Wallet?.transactionId);

  if (!tWallet2Wallet) {
    if (retryCount < 3) {
      console.warn(
        `missing transaction reference on BookTransferSuccessful anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
      );
      console.warn(`Retrying attempt ${retryCount + 1}) wait ${waitCount} ...`);
      return setTimeout(
        () => BookTransferSuccessful(data, retryCount + 1, waitCount + 5000),
        waitCount
      );
    }

    console.error(`max retries reached for anchorPaymentId ${anchorPaymentId}`);
    console.error(
      `missing transaction reference on BookTransferSuccessful ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  const transaction: any = await TransactionModel.findByPk(tWallet2Wallet.transactionId);
  if (!transaction) {
    console.error(
      `missing parent transaction reference on BookTransferSuccessful ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }
  console.log(transaction, tWallet2Wallet);

  if (transaction.status !== "SUCCESS")
    await TransactionModel.update({ status: "SUCCESS" }, { where: { id: transaction.id } });
}

async function BookTransferFailed(data: any, retryCount: number = 0, waitCount: number = 5000) {
  console.log("################### NIP TRANSFER FAILED ###################");
  console.log(data);
  console.log("#########################################");

  const walletService = new WalletService();
  const anchorPaymentId = data.relationships.transfer.data.id;
  const failureReason = data.attributes.failureReason;

  const tWallet2Wallet = await TransactionWallet2WalletModel.findOne({
    where: { anchorPaymentId: anchorPaymentId }
  });

  if (!tWallet2Wallet) {
    if (retryCount < 3) {
      console.warn(
        `missing transaction reference on BookTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
      );
      console.warn(`Retrying attempt ${retryCount + 1}) wait ${waitCount} ...`);
      return setTimeout(
        () => BookTransferFailed(data, retryCount + 1, waitCount + 5000),
        waitCount
      );
    }

    console.error(`max retries reached for anchorPaymentId ${anchorPaymentId}`);
    console.error(
      `missing transaction reference on BookTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  const transaction: any = await TransactionModel.findByPk(tWallet2Wallet.transactionId);
  if (!transaction) {
    console.error(
      `missing parent transaction reference on BookTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  if (failureReason === "INSUFFICIENT_BALANCE") {
    await TransactionModel.update({ status: "FAILED" }, { where: { id: transaction.id } });
  } else {
    await TransactionModel.update({ status: "REVERSED" }, { where: { id: transaction.id } });

    let initiatorWallet: any = (await walletService.getWallet(transaction.initiatingWallet)).wallet;
    if (!initiatorWallet) {
      console.error(
        `could not get user wallet on nip.transfer.failed event BookTransferFailed ${anchorPaymentId} walletId ${transaction.initiating_wallet_id}`
      );
      return;
    }

    initiatorWallet = (await walletService.deposit(initiatorWallet.id, transaction.amount)).wallet;
    console.log(initiatorWallet);

    let source = "reversal transaction";
    let initiatorStatement = `reversal for failed transaction ${transaction.id}`;

    const initiatorLog = await TransactionLogModel.create({
      transactionId: transaction.id,
      walletId: initiatorWallet.id,
      entryType: "CREDIT",
      amount: transaction.amount,
      transactionType: "DEPOSIT",
      transactionStatus: "SUCCESS",
      paymentMethod: "REVERSAL",
      statement: initiatorStatement,
      balance: initiatorWallet.balance
    });
    console.log(initiatorLog);
  }
}

async function NIPTransferSuccessful(data: any, retryCount: number = 0, waitCount: number = 5000) {
  console.log("################### NIP TRANSFER SUCCESSFUL ###################");
  console.log(data);
  console.log("#########################################");

  const anchorPaymentId = data.relationships.transfer.data.id;

  const tWallet2Other = await TransactionWallet2OtherModel.findOne({
    where: { anchorPaymentId: anchorPaymentId }
  });
  console.log(tWallet2Other);
  console.log(tWallet2Other?.transactionId);

  if (!tWallet2Other) {
    if (retryCount < 3) {
      console.warn(
        `missing transaction reference on NIPTransferSuccessful anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
      );
      console.warn(`Retrying attempt ${retryCount + 1}) wait ${waitCount} ...`);
      return setTimeout(
        () => NIPTransferSuccessful(data, retryCount + 1, waitCount + 5000),
        waitCount
      );
    }

    console.error(
      `Max retries reached for NIPTransferSuccessful anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    console.error(
      `missing transaction reference on NIPTransferSuccessful anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  const transaction: any = await TransactionModel.findByPk(tWallet2Other.transactionId);
  if (!transaction) {
    console.error(
      `missing parent transaction reference on NIPTransferSuccessful event anchorTransactionId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }
  console.log(transaction, tWallet2Other);

  if (transaction.status !== "SUCCESS")
    await TransactionModel.update({ status: "SUCCESS" }, { where: { id: transaction.id } });
}

//
async function NIPTransferFailed(data: any, retryCount: number = 0, waitCount: number = 5000) {
  console.log("################### NIP TRANSFER FAILED ###################");
  console.log(data);
  console.log("#########################################");

  const walletService = new WalletService();
  const anchorPaymentId = data.relationships.transfer.data.id;

  const tWallet2Other = await TransactionWallet2OtherModel.findOne({
    where: { anchorPaymentId: anchorPaymentId }
  });
  if (!tWallet2Other) {
    if (retryCount < 3) {
      console.warn(
        `missing transaction reference on NIPTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
      );
      console.warn(`Retrying attempt ${retryCount + 1}) wait ${waitCount} ...`);
      return setTimeout(() => NIPTransferFailed(data, retryCount + 1, waitCount + 5000), waitCount);
    }

    console.error(
      `Max retries reached for NIPTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    console.error(
      `missing transaction reference on NIPTransferFailed anchorTransactionId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  const transaction: any = await TransactionModel.findByPk(tWallet2Other.transactionId);
  if (!transaction) {
    console.error(
      `missing parent transaction reference on NIPTransferFailed anchorPaymentId ${anchorPaymentId} eventId ${data.id}`
    );
    return;
  }

  if (transaction.status !== "REVERSED" && transaction.status !== "FAILED") {
    await TransactionModel.update({ status: "REVERSED" }, { where: { id: transaction.id } });

    let initiatorWallet: any = (await walletService.getWallet(transaction.initiatingWallet))
      ?.wallet;
    if (!initiatorWallet) {
      console.error(
        `could not get user wallet on NIPTransferFailed anchorTransactionId ${anchorPaymentId} walletId ${transaction.initiatingWallet}`
      );
      return;
    }

    initiatorWallet = (await walletService.deposit(initiatorWallet.id, transaction.amount)).wallet;
    console.log(initiatorWallet);

    let source = "reversal transaction";
    let initiatorStatement = `reversal for failed transaction ${transaction.id}`;

    const initiatorLog = await TransactionLogModel.create({
      transactionId: transaction.id,
      walletId: initiatorWallet.id,
      entryType: "CREDIT",
      amount: transaction.amount,
      transactionType: "DEPOSIT",
      transactionStatus: "SUCCESS",
      paymentMethod: "REVERSAL",
      statement: initiatorStatement,
      balance: initiatorWallet.balance
    });
    console.log(initiatorLog);
  }
}

async function virtualNubanOpened(data: any) {
  console.log("################### VIRTUAL NUBAN OPENED");
  console.log(data);
  console.log("#########################################");

  const customerId = data.relationships.customer.data.id;
  const virtualNubanId: any = data.relationships.virtualAccount.data.id;
  const virtualNuban: any = (await AnchorService.GetVirtualNuban(virtualNubanId)).data;

  console.log(virtualNuban, virtualNuban.attributes.bank);

  const anchorCustomer = await AnchorModel.findOne({ where: { customerId: customerId } });
  if (!anchorCustomer) {
    console.error(`could not find anchorCustomer on virtualNuban.opened event ${customerId}`);
    return;
  }

  if (!anchorCustomer.defaultVirtualNuban)
    await AnchorModel.update(
      {
        defaultVirtualNuban: virtualNuban.id
      },
      { where: { id: anchorCustomer.id } }
    );

  const vn = await AnchorVNModel.create({
    userId: anchorCustomer.userId,
    customerId: anchorCustomer.customerId,

    virtualNubanId: virtualNuban.id,
    bankId: virtualNuban.attributes.bank.id,
    bankName: virtualNuban.attributes.bank.name,
    bankNipCode: virtualNuban.attributes.bank.nipCode,

    accountName: virtualNuban.attributes.accountName,
    accountNumber: virtualNuban.attributes.accountNumber,

    isDefault: virtualNuban.attributes.isDefault,
    isPermanent: virtualNuban.attributes.permanent,
    status: virtualNuban.attributes.status
  });

  console.log(vn);
}

async function accountOpened(data: any) {
  console.log("################### ACCOUNT OPENED");
  console.log(data);
  console.log("#########################################");

  //
  const customerId = data.relationships.customer.data.id;
  const accountId = data.relationships.account.data.id;

  console.log(customerId);
  console.log(accountId);

  const anchorCustomer = await AnchorModel.findOne({ where: { customerId: customerId } });
  if (!anchorCustomer) {
    console.error(`could not find anchorCustomer on account.opened event ${customerId}`);
    return;
  }

  if (!anchorCustomer.depositAccountId)
    await AnchorModel.update(
      {
        depositAccountId: accountId
      },
      { where: { id: anchorCustomer.id } }
    );

  console.log(await AnchorModel.findByPk(anchorCustomer.id));
}

async function customerIdentificationApproved(data: any) {
  console.log("################### CUSTOMER IDENTIFICATION APPROVED");
  console.log(data);
  console.log("#########################################");

  //
  const customerId = data.relationships.customer.data.id;
  console.log("customerId", customerId);

  const KYC_FEE_CHARGE: number = 200;

  const anchorCustomer = await AnchorModel.findOne({ where: { customerId: customerId } });
  if (!anchorCustomer) {
    console.error(
      `could not find anchorCustomer on customer.identification.approved event ${customerId}`
    );
    return;
  }

  console.log(anchorCustomer);

  if (anchorCustomer.status !== "approved")
    await AnchorModel.update(
      {
        status: "approved",
        message: "KYC initiated successfully"
      },
      { where: { id: anchorCustomer.id } }
    );

  // charge kyc fee
  const walletService = new WalletService();
  const wallet: any = (await walletService.getUserWallet(anchorCustomer.userId)).wallet;
  if (!wallet) {
    console.error(
      `could not find user wallet on payment.settled event ${anchorCustomer.userId} eventId ${data.id}`
    );
    return; // critical error
  }
  const initiatorWallet = (await walletService.withdraw(wallet.id, KYC_FEE_CHARGE)).wallet;

  let initiatorStatement = "KYC 2 Verification Fee";

  const transaction = await TransactionModel.create({
    transactionType: "DEPOSIT",
    amount: KYC_FEE_CHARGE,
    charges: 0,
    userId: anchorCustomer.userId,
    initiatingWallet: wallet.id,
    status: "SUCCESS"
  });
  const tWalletDepositModel = await TransactionSystemChargeModel.create({
    transactionId: transaction.id,
    initiator: "SYSTEM",
    reason: initiatorStatement
  });
  console.log(transaction, tWalletDepositModel);

  const initiatorLog = await TransactionLogModel.create({
    transactionId: "transaction.id",
    walletId: initiatorWallet.id,
    entryType: "DEBIT",
    amount: KYC_FEE_CHARGE,
    transactionType: "WITHDRAW",
    transactionStatus: "SUCCESS",
    paymentMethod: "WALLET",
    statement: initiatorStatement,
    balance: initiatorWallet.balance
  });

  if (anchorCustomer.depositAccountId) return;

  const depositAccount = await AnchorService.CreateDepositAccount(customerId);
  console.log("depositAccount", depositAccount);
  if (!depositAccount.data.id) return;

  await AnchorModel.update(
    {
      depositAccountId: depositAccount.data.id
    },
    { where: { id: anchorCustomer.id } }
  );
}

async function customerIdentificationFailed(data: any) {
  console.log("################### CUSTOMER IDENTIFICATION FAILED");
  console.log(data);
  console.log("#########################################");

  const customerId = data.relationships.customer.data.id;
  console.log("customerId", customerId);

  const anchorCustomer = await AnchorModel.findOne({ where: { customerId: customerId } });
  if (!anchorCustomer) {
    console.error(
      `could not find anchorCustomer on customer.identification.failed event customerId ${customerId}`
    );
    return;
  }
  console.log(anchorCustomer);

  if (anchorCustomer.status !== "failed")
    await AnchorModel.update(
      {
        status: "failed",
        message: "KYC failed"
      },
      { where: { id: anchorCustomer.id } }
    );
}

async function customerIdentificationRejected(data: any) {}
async function customerIdentificationError(data: any) {}

async function foo() {}

export default {
  NIPTransferFailed,
  NIPTransferSuccessful,
  paymentSettled,
  virtualNubanOpened,
  accountOpened,
  customerIdentificationApproved,
  customerIdentificationFailed,
  BookTransferSuccessful,
  BookTransferFailed
};
