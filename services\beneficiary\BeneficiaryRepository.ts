import IBeneficiaryRepository from "./IBeneficiaryRepository";
import UserBeneficiaryModel from "../../shared/Model/UserBeneficiaryModel";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class BeneficiaryRepository implements IBeneficiaryRepository {
  async addBeneficiary(userId: string, fields: any): Promise<any> {
    const { alias, accountNumber, bankCode, bankName, accountName } = fields;
    const beneficiary = await UserBeneficiaryModel.create({
      userId: userId,
      alias: alias,
      bankCode: bankCode,
      bankAccountNumber: accountNumber,
      bankName: bankName,
      bankAccountName: accountName
    });

    return beneficiary;
  }

  async updateBeneficiary(userId: string, aliasId: string, fields: any): Promise<any> {
    const { alias, accountNumber, bankCode, bankName, accountName } = fields;
    const beneficiary = await UserBeneficiaryModel.update(
      {
        alias: alias,
        bankCode: bankCode,
        bankAccountNumber: accountNumber,
        bankName: bankName,
        bankAccountName: accountName
      },
      {
        where: {
          userId: userId,
          alias: aliasId
        }
      }
    );

    return await this.getBeneficiary(userId, alias);
  }

  async removeBeneficiary(userId: string, beneficiaryId: string): Promise<any> {
    await UserBeneficiaryModel.destroy({ where: { userId: userId, alias: beneficiaryId } });
  }

  async getBeneficiaries(userId: string): Promise<any> {
    return await UserBeneficiaryModel.findAll({ where: { userId: userId } });
  }

  async getBeneficiary(userId: string, beneficiaryId: string): Promise<any> {
    return await UserBeneficiaryModel.findOne({ where: { userId: userId, alias: beneficiaryId } });
  }

  async searchBeneficiary(aliasString: string): Promise<any> {}
}

export default BeneficiaryRepository;
