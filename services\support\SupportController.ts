import { Request, Response, NextFunction } from "express";
import AnchorService from "../../AnchorService";
import SupportResolveAccountDetails from "./SupportResolveAccountDetails";
import SupportListBanks from "./SupportListBanks";

class SupportController {
  public static async resolveAccountDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const { account_number, bank_code } = req.body;

      const supportResolveAccountDetails = new SupportResolveAccountDetails();
      const { account, bank } = await supportResolveAccountDetails.init(bank_code, account_number);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["account"] = {};
      data["account"]["account_name"] = account.accountName;
      data["account"]["account_number"] = account.accountNumber;

      data["bank"] = {};
      data["bank"]["id"] = bank.id;
      data["bank"]["name"] = bank.name;
      data["bank"]["nipcode"] = bank.nipCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async listBanks(req: Request, res: Response, next: NextFunction) {
    try {
      const supportListBanks = new SupportListBanks();
      const banks = await supportListBanks.init();

      const bankList = banks.map((bank: any) => {
        return {
          id: bank.id,
          name: bank.attributes.name,
          nipcode: bank.attributes.nipCode
        };
      });

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["banks"] = bankList;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }
}

export default SupportController;
