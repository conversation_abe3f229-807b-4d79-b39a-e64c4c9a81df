import { config } from "dotenv";
config();

import Mailer from "./mailer";

async function testProfessionalWaitlistEmail() {
  const firstName = "Test";
  const email = "<EMAIL>"; // Replace with your email for testing
  
  const subject = "Welcome to Agentpesa Waitlist!";
  const body = `
   <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Welcome to Agentpesa</title>
    <style>
        /* Basic resets and font styles */
        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }
        a {
            color: inherit;
            text-decoration: none;
        }
        
        /* Responsive styles for mobile */
        @media screen and (max-width: 600px) {
            .content-table {
                width: 100% !important;
            }
            .header-text {
                font-size: 28px !important;
            }
            .body-text {
               font-size: 16px !important;
               line-height: 1.5 !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <!-- Main Content Table -->
                <table align="center" border="0" cellpadding="0" cellspacing="0" class="content-table" style="width: 600px; max-width: 600px; border-spacing: 0; background-color: #ffffff; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); border-radius: 8px; overflow: hidden;">
                    
                    <!-- Header Section -->
                    <tr>
                        <td align="center" style="padding: 40px 0 32px 0;">
                            <h1 class="header-text" style="margin: 0; color: #1f2937; font-size: 36px; font-weight: bold; letter-spacing: 1px;">
                                Agentpesa
                            </h1>
                        </td>
                    </tr>

                    <!-- Content Section -->
                    <tr>
                        <td style="padding: 0 40px 40px 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #1f2937; font-size: 28px; font-weight: bold;">
                                Welcome to the Waitlist, ${firstName}!
                            </h2>
                            
                            <p class="body-text" style="margin: 0 0 20px 0; color: #4b5563; font-size: 18px; line-height: 1.6;">
                                Thank you for joining the Agentpesa waitlist! We're excited to have you on board as we prepare to revolutionize digital payments in Nigeria.
                            </p>
                            
                            <!-- Social Media Links -->
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 20px auto;">
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">
                                            Follow us on social media:
                                        </p>
                                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                            <tr>
                                                <!-- Facebook -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.facebook.com/61577778871615/posts/122121339824925962/?mibextid=rS40aB7S9Ucbxw6v" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #1877f2; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://cdn.litmus.com/social/facebook-24.png" alt="Facebook" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- X (Twitter) -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://x.com/agentpesa/status/1953764937261556068?t=U7zYPPac6SewLQhObQr6qQ&s=19" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #000000; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://cdn.litmus.com/social/twitter-24.png" alt="Twitter" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- Instagram -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.instagram.com/p/DNFzKrpCT-c/?igsh=NTc4MTIwNjQ2YQ==" target="_blank" style="display: inline-block; width: 32px; height: 32px; background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://cdn.litmus.com/social/instagram-24.png" alt="Instagram" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- LinkedIn -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.linkedin.com/posts/agentpesa_agentpesa-weekendfun-digitalpayments-activity-7359532138024595456-DOyQ?utm_source=share&utm_medium=member_desktop&rcm=ACoAACviJ0YBTXuW25FMgX-il9nsczv7hW8S72Q" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #0077b5; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://cdn.litmus.com/social/linkedin-24.png" alt="LinkedIn" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `;

  try {
    console.log("Sending professional email test...");
    await Mailer.send(email, subject, body);
    console.log("✅ Professional email sent successfully!");
    console.log("📧 Check your email to see the professional Litmus icons");
  } catch (error) {
    console.error("❌ Error sending email:", error);
  }
}

// Run the test
testProfessionalWaitlistEmail();
