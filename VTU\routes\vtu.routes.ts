// Update your VTU/routes/vtu.routes.ts
import { Router } from "express";
import { VTUController } from "../controllers/vtu.controller";
import dataRoutes from "./data.routes"; // Add this import
import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";

const router = Router();
const vtuController = new VTUController();

// Airtime routes (protected)
router.post(
  "/airtime/purchase",
  AuthProtectionMiddleware,
  vtuController.purchaseAirtime.bind(vtuController)
);

// Utility routes
router.get("/networks", vtuController.getNetworkProviders.bind(vtuController));
router.post("/detect-network", vtuController.detectNetwork.bind(vtuController));
router.get("/billers/airtime", vtuController.getAirtimeBillers.bind(vtuController));

// Add data routes
router.use("/data", dataRoutes);

export default router;
