import IWalletRepository from "./IWalletRepository";
import Wallet from "./Wallet";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class WalletDeposit {
  private wallet: Wallet;

  private _repository: IWalletRepository;

  constructor(walletRepository: IWalletRepository) {
    this._repository = walletRepository;
  }

  async init(walletId: string, amount: number) {
    const wallet = await this._repository.getWallet(walletId);
    if (!wallet)
      throw new AppException(domainError.WALLET_NOT_FOUND, `wallet ${walletId} does not exist`);

    this.wallet = wallet;

    if (amount < 0.1) throw new AppException(domainError.TEST_ERROR, "invalid amount requested");

    this.wallet.deposit(amount);
    await this._repository.save(this.wallet);

    return { wallet: this.wallet };
  }
}

export default WalletDeposit;
