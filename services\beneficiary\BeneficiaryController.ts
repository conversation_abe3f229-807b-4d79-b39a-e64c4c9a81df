import { Request, Response, NextFunction } from "express";
import AddBeneficiary from "./AddBeneficiary";
import UpdateBeneficiary from "./UpdateBeneficiary";
import GetBeneficiary from "./GetBeneficiary";
import GetBeneficiaries from "./GetBeneficiaries";
import RemoveBeneficiary from "./RemoveBeneficiary";
import BeneficiaryRepository from "./BeneficiaryRepository";

class BeneficiaryController {
  public static async addBeneficiary(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { account_name, account_number, alias, bank_code, bank_name } = req.body;

      const beneficiaryRepository = new BeneficiaryRepository();

      const addBeneficiary = new AddBeneficiary(beneficiaryRepository);

      const action = await addBeneficiary.init(
        userId,
        account_name,
        account_number,
        bank_code,
        bank_name,
        alias
      );
      const { beneficiary } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["beneficiary"] = {};
      data["beneficiary"]["id"] = beneficiary.id;
      data["beneficiary"]["alias"] = beneficiary.alias;
      data["beneficiary"]["account_name"] = beneficiary.bankAccountName;
      data["beneficiary"]["account_number"] = beneficiary.bankAccountNumber;
      data["beneficiary"]["bank_name"] = beneficiary.bankName;
      data["beneficiary"]["bank_code"] = beneficiary.bankCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async updateBeneficiary(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias: id } = req.params;
      const { account_name, account_number, alias, bank_code, bank_name } = req.body;

      const beneficiaryRepository = new BeneficiaryRepository();
      const updateBeneficiary = new UpdateBeneficiary(beneficiaryRepository);

      const action = await updateBeneficiary.init(
        id,
        userId,
        account_name,
        account_number,
        bank_code,
        bank_name,
        alias
      );
      const { beneficiary } = action;
      console.log(action);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["beneficiary"] = {};
      data["beneficiary"]["id"] = beneficiary.id;
      data["beneficiary"]["alias"] = beneficiary.alias;
      data["beneficiary"]["account_name"] = beneficiary.bankAccountName;
      data["beneficiary"]["account_number"] = beneficiary.bankAccountNumber;
      data["beneficiary"]["bank_name"] = beneficiary.bankName;
      data["beneficiary"]["bank_code"] = beneficiary.bankCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getBeneficiaries(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;

      const beneficiaryRepository = new BeneficiaryRepository();

      const getBeneficiaries = new GetBeneficiaries(beneficiaryRepository);

      const action = await getBeneficiaries.init(userId);

      const beneficiariesList = action.beneficiaries.map((beneficiary: any) => {
        return {
          id: beneficiary.id,
          alias: beneficiary.alias,
          account_name: beneficiary.bankAccountName,
          account_number: beneficiary.bankAccountNumber,
          bank_name: beneficiary.bankName,
          bank_code: beneficiary.bankCode
        };
      });

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = beneficiariesList;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getBeneficiary(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias } = req.params;

      const beneficiaryRepository = new BeneficiaryRepository();

      const getBeneficiary = new GetBeneficiary(beneficiaryRepository);

      const action = await getBeneficiary.init(userId, alias);
      const { beneficiary } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["beneficiary"] = {};
      data["beneficiary"]["id"] = beneficiary.id;
      data["beneficiary"]["alias"] = beneficiary.alias;
      data["beneficiary"]["account_name"] = beneficiary.bankAccountName;
      data["beneficiary"]["account_number"] = beneficiary.bankAccountNumber;
      data["beneficiary"]["bank_name"] = beneficiary.bankName;
      data["beneficiary"]["bank_code"] = beneficiary.bankCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async deleteBeneficiary(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias } = req.params;

      const beneficiaryRepository = new BeneficiaryRepository();

      const removeBeneficiary = new RemoveBeneficiary(beneficiaryRepository);

      const action = await removeBeneficiary.init(userId, alias);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }
}

export default BeneficiaryController;
