import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class Anchor extends Model<InferAttributes<Anchor>, InferCreationAttributes<Anchor>> {
  declare id: CreationOptional<string>;
  declare userId: string;
  declare customerId: CreationOptional<string>;

  declare depositAccountId: CreationOptional<string>;
  declare defaultVirtualNuban: CreationOptional<string>;

  declare kycLevel: CreationOptional<number>;
  declare bvn: CreationOptional<string>;
  declare phoneNumber: CreationOptional<string>;
  declare dateOfBirth: CreationOptional<string>;
  declare selfieImage: CreationOptional<string>;
  declare status: CreationOptional<string>;
  declare message: CreationOptional<string>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Anchor.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    customerId: {
      field: "customer_id",
      type: DataTypes.STRING
    },
    depositAccountId: {
      field: "deposit_account_id",
      type: DataTypes.STRING
    },
    defaultVirtualNuban: {
      field: "default_virtual_nuban",
      type: DataTypes.STRING
    },
    kycLevel: {
      field: "kyc_level",
      type: DataTypes.STRING
    },
    bvn: {
      field: "bvn",
      type: DataTypes.STRING
    },
    phoneNumber: {
      field: "phone_number",
      type: DataTypes.STRING
    },
    dateOfBirth: {
      field: "date_of_birth",
      type: DataTypes.STRING
    },
    selfieImage: {
      field: "selfie_image",
      type: DataTypes.STRING
    },
    status: {
      type: DataTypes.STRING
    },
    message: {
      type: DataTypes.STRING
    },

    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "anchor",
    tableName: "anchor",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Anchor;
