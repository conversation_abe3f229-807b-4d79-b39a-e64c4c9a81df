import { getNetworkFromPhone } from "../config/vtu.config";

export class CommonValidation {
  static validatePhoneNumber(
    phoneNumber: string
  ): {
    isValid: boolean;
    formatted: string;
    network: string;
    error?: string;
  } {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    // Check if it's a valid Nigerian number
    let formatted = "";

    if (cleaned.startsWith("234") && cleaned.length === 13) {
      formatted = cleaned;
    } else if (cleaned.startsWith("0") && cleaned.length === 11) {
      formatted = "234" + cleaned.substring(1);
    } else if (cleaned.length === 10) {
      formatted = "234" + cleaned;
    } else {
      return {
        isValid: false,
        formatted: "",
        network: "",
        error: "Invalid phone number format"
      };
    }

    // Get network from phone number
    const network = getNetworkFromPhone("0" + formatted.substring(3));

    if (network === "UNKNOWN") {
      return {
        isValid: false,
        formatted: "",
        network: "",
        error: "Unable to detect network provider"
      };
    }

    return {
      isValid: true,
      formatted,
      network
    };
  }

  static validateAmount(
    amount: number,
    serviceType: string = "AIRTIME"
  ): {
    isValid: boolean;
    error?: string;
  } {
    if (serviceType === "AIRTIME") {
      if (amount < 50) {
        return {
          isValid: false,
          error: "Minimum airtime amount is ₦50"
        };
      }

      if (amount > 10000) {
        return {
          isValid: false,
          error: "Maximum airtime amount is ₦10,000"
        };
      }
    }

    return { isValid: true };
  }
}
