import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransactionWallet2Wallet extends Model<
  InferAttributes<TransactionWallet2Wallet>,
  InferCreationAttributes<TransactionWallet2Wallet>
> {
  declare transactionId: string;
  declare receivingWalletId: CreationOptional<string>;

  declare anchorPaymentId: String;
  declare anchorPaymentRef: String;

  declare reason: String;
}

TransactionWallet2Wallet.init(
  {
    transactionId: {
      field: "transaction_id",
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      validate: {
        isUUID: {
          args: 4,
          msg: "Transaction ID must be a valid UUID"
        },
        notEmpty: {
          msg: "Transaction ID cannot be empty"
        }
      }
    },
    receivingWalletId: {
      field: "receiving_wallet_id",
      type: DataTypes.UUID,
      allowNull: false,
      validate: {
        isUUID: {
          args: 4,
          msg: "Receiving wallet ID must be a valid UUID"
        },
        notEmpty: {
          msg: "Receiving wallet ID cannot be empty"
        },
        customValidator(value: string) {
          if (!value || value.trim().length === 0) {
            throw new Error("Receiving wallet ID is required");
          }
        }
      }
    },
    anchorPaymentId: {
      field: "anchor_payment_id",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Anchor payment ID must be between 0 and 100 characters"
        },
        isAlphanumeric: {
          msg: "Anchor payment ID must contain only letters and numbers"
        }
      }
    },
    anchorPaymentRef: {
      field: "anchor_payment_ref",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Anchor payment reference must be between 0 and 100 characters"
        }
      }
    },
    reason: {
      field: "reason",
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: "",
      validate: {
        len: {
          args: [0, 500],
          msg: "Reason must be between 0 and 500 characters"
        }
      }
    }
  },
  {
    sequelize,
    modelName: "transaction_wallet2wallet",
    tableName: "transaction_wallet2wallet",
    createdAt: false,
    updatedAt: false
  }
);

export default TransactionWallet2Wallet;
