import AppException from "../../AppException";
import { domainError } from "../../domainError";
import OTP from "./otp/OTPGenerate";
import jwt from "../../libraries/jwt";
import SMSER from "../../smser";
import AccountRepository from "./AccountRepository";

const accountRepository = new AccountRepository();

class AccountPhoneVerification {
  public static async RequestPhoneVerification(phone: string) {
    const isPhoneTaken: boolean = await accountRepository.phoneExists(phone);
    if (isPhoneTaken)
      throw new AppException(
        domainError.UNAVAILABLE_PHONE,
        "provided phone is already registered with another account"
      );

    const otp: any = await OTP.generate({ phone: phone });
    // await _smsOTP(otp.otpCode, phone);
    return { otpId: otp.otpId, otpCode: otp.otpCode };
  }

  public static async ConfirmPhoneVerification(otp_id: string, otp_code: string) {
    const otp: any = await OTP.retrieve(otp_id);
    if (!otp) throw new AppException(domainError.INVALID_OTP);
    if (otp.otp_code !== otp_code) throw new AppException(domainError.INVALID_OTP);

    if (new Date() > new Date(parseInt(otp.generated_time) + parseInt(otp.expiration_time)))
      throw new AppException(domainError.INVALID_OTP);

    const token: string = await jwt.sign({
      otpId: otp_id,
      data: {
        account_type: "native",
        phone: otp.data.phone,
        email: null
      },
      generated_time: new Date().getTime(),
      expiration_time: 300000
    });

    return { otpId: otp.otp_id, token: token };
  }
}

async function _smsOTP(code: string, phone: string) {
  const OTPMailContent = {
    body: `AGENTPESA: ${code} is your phone verification code and is valid for 10 minutes. If you did not request this code, please ignore this message. Thank you.`
  };

  await SMSER.send(phone, OTPMailContent["body"]);
}

export default AccountPhoneVerification;
