import AnchorService from "../../AnchorService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class SupportListBanks {
  constructor() {}

  async init(): Promise<any> {
    let banks = await AnchorService.ListBanks();
    if (!banks?.data)
      throw new AppException(
        domainError.TEST_ERROR,
        "could not resolve account details at the moment "
      );

    return banks.data;
  }
}

export default SupportListBanks;
