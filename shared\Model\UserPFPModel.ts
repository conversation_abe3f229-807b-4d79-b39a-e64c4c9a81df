import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class AccountPFPModel extends Model<
  InferAttributes<AccountPFPModel>,
  InferCreationAttributes<AccountPFPModel>
> {
  declare id: CreationOptional<string>;
  declare userId: string;

  declare fileId: string;
  declare name: string;
  declare url: string;
  declare thumbnailUrl: string;
  declare fileType: string;
  declare fileExtension: string;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

AccountPFPModel.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    fileId: {
      field: "file_id",
      type: DataTypes.STRING
    },
    name: {
      field: "name",
      type: DataTypes.STRING
    },
    url: {
      field: "url",
      type: DataTypes.STRING
    },
    thumbnailUrl: {
      field: "thumbnail_url",
      type: DataTypes.STRING
    },
    fileType: {
      field: "file_type",
      type: DataTypes.STRING
    },
    fileExtension: {
      field: "file_extension",
      type: DataTypes.STRING
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user_pfp",
    tableName: "user_pfp",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default AccountPFPModel;
