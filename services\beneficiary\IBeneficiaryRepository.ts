interface IBeneficiaryRepository {
  addBeneficiary(userId: string, fields: any): Promise<any>;
  updateBeneficiary(userId: string, aliasId: string, fields: any): Promise<any>;
  removeBeneficiary(userId: string, beneficiaryId: string): Promise<any>;
  getBeneficiaries(userId: string): Promise<any>;
  getBeneficiary(userId: string, beneficiaryId: string): Promise<any>;
  searchBeneficiary(aliasString: string): Promise<[]>;
}

export default IBeneficiaryRepository;
