import AppException from "../../AppException";
import { domainError } from "../../domainError";
import OTP from "./otp/OTPGenerate";
import jwt from "../../libraries/jwt";
import Mailer from "../../mailer";
import AccountRepository from "./AccountRepository";

const accountRepository = new AccountRepository();

class AccountMailVerification {
  public static async RequestEmailVerification(email: string) {
    const isEmailTaken: boolean = await accountRepository.emailExists(email);
    if (isEmailTaken)
      throw new AppException(
        domainError.UNAVAILABLE_EMAIL_ADDRESS,
        "provided email is already registered with another account"
      );

    const otp: any = await OTP.generate({ email: email });
    await _mailOTP(otp.otpCode, email);
    return { otpId: otp.otpId, otpCode: otp.otpCode };
  }

  static async ConfirmEmailVerification(otp_id: string, otp_code: string) {
    const otp: any = await OTP.retrieve(otp_id);
    if (!otp) throw new AppException(domainError.INVALID_OTP);
    if (otp.otp_code !== otp_code) throw new AppException(domainError.INVALID_OTP);

    if (new Date() > new Date(parseInt(otp.generated_time) + parseInt(otp.expiration_time)))
      throw new AppException(domainError.INVALID_OTP);

    const token: string = await jwt.sign({
      otpId: otp_id,
      data: {
        account_type: "native",
        email: otp.data.email,
        phone: null
      },
      generated_time: new Date().getTime(),
      expiration_time: 300000
    });

    return { otpId: otp.otp_id, token: token };
  }
}

async function _mailOTP(code: string, email: string) {
  const OTPMailContent = {
    subject: "AgentPesa: Email Confirmation OTP",
    body: `<div class="email-container">
        <h2>${code}</h2>
        <p>>Use the above code to confirm your email</p>
        <p>This code is valid for 10 minutes. If you did not request this code, please ignore this email.</p>
        <div class="footer">
            <p>Thank you,</p>
            <p>AgentPesa</p>
        </div>
    </div>`
  };

  await Mailer.send(email, OTPMailContent["subject"], OTPMailContent["body"]);
}

export default AccountMailVerification;
