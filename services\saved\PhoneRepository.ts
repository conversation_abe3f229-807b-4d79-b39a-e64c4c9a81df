import IPhoneRepository from "./PhoneRepository";
import SavedPhoneModel from "../../shared/Model/SavedPhoneModel";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class PhoneRepository implements IPhoneRepository {
  async addPhone(userId: string, fields: any): Promise<any> {
    const { alias, phoneNumber } = fields;
    const beneficiary = await SavedPhoneModel.create({
      userId: userId,
      alias: alias,
      phone: phoneNumber
    });

    return beneficiary;
  }

  async updatePhone(userId: string, alias: string, fields: any): Promise<any> {
    const { phoneNumber } = fields;
    const beneficiary = await SavedPhoneModel.update(
      { phone: phoneNumber },
      {
        where: {
          userId: userId,
          alias: alias
        }
      }
    );

    return await this.getPhone(userId, alias);
  }

  async removePhone(userId: string, alias: string): Promise<any> {
    await SavedPhoneModel.destroy({ where: { userId: userId, alias: alias } });
  }

  async getPhones(userId: string): Promise<any> {
    return await SavedPhoneModel.findAll({ where: { userId: userId } });
  }

  async getPhone(userId: string, beneficiaryId: string): Promise<any> {
    return await SavedPhoneModel.findOne({ where: { userId: userId, alias: beneficiaryId } });
  }
}

export default PhoneRepository;
