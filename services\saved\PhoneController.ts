import { Request, Response, NextFunction } from "express";
import AddPhone from "./AddPhone";
import UpdatePhone from "./UpdatePhone";
import GetPhone from "./GetPhone";
import GetPhones from "./GetPhones";
import RemovePhone from "./RemovePhone";
import PhoneRepository from "./PhoneRepository";

class PhoneController {
  public static async addPhone(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { phone, alias } = req.body;

      const phoneRepository = new PhoneRepository();
      const addPhone = new AddPhone(phoneRepository);

      const action = await addPhone.init(userId, phone, alias);
      const { phone: p } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["phone"] = {};
      data["phone"]["id"] = p.id;
      data["phone"]["alias"] = p.alias;
      data["phone"]["phone"] = p.phone;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async updatePhone(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias: id } = req.params;
      const { phone, alias } = req.body;

      const phoneRepository = new PhoneRepository();
      const updatePhone = new UpdatePhone(phoneRepository);

      const action = await updatePhone.init(id, userId, phone, alias);
      const { phone: p } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["phone"] = {};
      data["phone"]["id"] = p.id;
      data["phone"]["alias"] = p.alias;
      data["phone"]["phone"] = p.phone;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getPhones(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;

      const phoneRepository = new PhoneRepository();
      const getPhones = new GetPhones(phoneRepository);
      const action = await getPhones.init(userId);

      const phonesList = action.phones.map((phone: any) => {
        return { id: phone.id, alias: phone.alias, phone: phone.phone };
      });

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = phonesList;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getPhone(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias } = req.params;

      const phoneRepository = new PhoneRepository();
      const getPhone = new GetPhone(phoneRepository);

      const action = await getPhone.init(userId, alias);
      const { phone: p } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["phone"] = {};
      data["phone"]["id"] = p.id;
      data["phone"]["alias"] = p.alias;
      data["phone"]["phone"] = p.phone;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async deletePhone(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { alias } = req.params;

      const phoneRepository = new PhoneRepository();
      const removePhone = new RemovePhone(phoneRepository);
      const action = await removePhone.init(userId, alias);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }
}

export default PhoneController;
