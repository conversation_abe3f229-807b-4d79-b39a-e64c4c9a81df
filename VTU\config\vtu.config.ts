export const VTU_CONFIG = {
  ANCHOR: {
    BASE_URL: "https://api.sandbox.getanchor.co",
    ACCOUNT_ID: "**************-anc_acc",
    ENDPOINTS: {
      AIRTIME: "/api/v1/bills",
      DATA: "/api/v1/bills",
      VERIFY: "/api/v1/bills/verify",
      BILLERS: "/api/v1/bills/billers",
      AIRTIME_BILLERS: "/api/v1/bills/billers?category=airtime",
      DATA_BILLERS: "/api/v1/bills/billers?category=data",
      DATA_PRODUCTS: "/api/v1/bills/billers"
    },
    NETWORKS: {
      MTN: {
        code: "MTN",
        name: "MTN Nigeria",
        serviceId: "**************-anc_blr",
        provider: "mtn"
      },
      GLO: {
        code: "GLO",
        name: "Globacom Nigeria",
        serviceId: "***************-anc_blr",
        provider: "glo"
      },
      AIRTEL: {
        code: "AIRTEL",
        name: "Airtel Nigeria",
        serviceId: "***************-anc_blr",
        provider: "airtel"
      },
      "9MOBILE": {
        code: "9MOBILE",
        name: "9mobile Nigeria",
        serviceId: "***************-anc_blr",
        provider: "9mobile"
      }
    }
  },
  TRANSACTION_STATUS: {
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    SUCCESSFUL: "SUCCESSFUL",
    FAILED: "FAILED",
    REVERSED: "REVERSED"
  },
  SERVICE_TYPES: {
    AIRTIME: "AIRTIME",
    DATA: "DATA",
    CABLE_TV: "CABLE_TV",
    ELECTRICITY: "ELECTRICITY"
  }
};

export const getNetworkFromPhone = (phoneNumber: string): string => {
  // Normalize phone number to start with 0
  let phone = phoneNumber.replace(/\D/g, "");

  // Convert to 0xxxxxxxxxx format
  if (phone.startsWith("234") && phone.length === 13) {
    phone = "0" + phone.substring(3);
  } else if (phone.length === 10) {
    phone = "0" + phone;
  }

  // MTN prefixes
  if (/^(0803|0806|0703|0706|0813|0816|0810|0814|0903|0906|0913|0916|0704)/.test(phone)) {
    return "MTN";
  }

  // GLO prefixes
  if (/^(0805|0807|0815|0811|0905|0915)/.test(phone)) {
    return "GLO";
  }

  // AIRTEL prefixes
  if (/^(0802|0808|0812|0701|0708|0902|0907|0901|0904|0912)/.test(phone)) {
    return "AIRTEL";
  }

  // 9MOBILE prefixes
  if (/^(0809|0817|0818|0909|0908)/.test(phone)) {
    return "9MOBILE";
  }

  return "UNKNOWN";
};
