import Joi from "joi";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

const WALLET_PIN_REGEXP = /^[0-9]{4}$/;

class WalletValidation {
  private static pinSchema = Joi.object({
    pin: Joi.string()
      .regex(WALLET_PIN_REGEXP)
      .required()
      .messages({
        "string.pattern.base": "pin must be a 4 digit code"
      })
  });

  public static async Pin(requestData: any): Promise<any | void> {
    const { pin } = requestData;
    try {
      return await WalletValidation.pinSchema.validateAsync({ pin });
    } catch (e) {
      const err: any = e;
      const message = err.message.replace(/\"/g, "");
      throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, message);
    }
  }
}

export default WalletValidation;
