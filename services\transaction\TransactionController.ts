import { Request, Response, NextFunction } from "express";
import TransactionRepository from "./TransactionRepository";
import WalletService from "../wallet/WalletService";
import TransactionWallet2Other from "./TransactionWallet2Other";
import TransactionWallet2Wallet from "./TransactionWallet2Wallet";
import TransactionViewMany from "./TransactionViewMany";
import TransactionView from "./TransactionView";

const transactionRepository = new TransactionRepository();

class TransactionController {
  public static async transactionWallet2Other(req: Request, res: Response, next: NextFunction) {
    try {
      const { amount, bank_code, bank_account_number } = req.body;

      if (!amount) {
        return res.status(400).json({
          success: false,
          message: "Amount is required"
        });
      }

      if (!bank_code) {
        return res.status(400).json({
          success: false,
          message: "Bank code is required"
        });
      }

      if (!bank_account_number) {
        return res.status(400).json({
          success: false,
          message: "Bank account number is required"
        });
      }

      const numericAmount = Number(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        return res.status(400).json({
          success: false,
          message: "Amount must be a positive number"
        });
      }

      if (numericAmount < 100) {
        return res.status(400).json({
          success: false,
          message: "Minimum withdrawal amount is ₦100"
        });
      }

      if (numericAmount > 1000000) {
        return res.status(400).json({
          success: false,
          message: "Maximum withdrawal amount is ₦1,000,000"
        });
      }

      if (!/^\d{6}$/.test(bank_code)) {
        return res.status(400).json({
          success: false,
          message: "Bank code must be exactly 6 digits"
        });
      }

      if (!/^\d{10}$/.test(bank_account_number)) {
        return res.status(400).json({
          success: false,
          message: "Bank account number must be exactly 10 digits"
        });
      }

      const walletService = new WalletService();
      const { userId } = res.locals.authenticated_user;

      const transactionWallet2Other = new TransactionWallet2Other(
        transactionRepository,
        walletService
      );

      transactionWallet2Other.setUserId(userId);
      transactionWallet2Other.setAmount(amount);
      transactionWallet2Other.setBankCode(bank_code);
      transactionWallet2Other.setAccountNumber(bank_account_number);

      const action = await transactionWallet2Other.init();
      const { transaction: t, wallet } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["transaction"] = {};
      data["transaction"]["id"] = t.id;
      data["transaction"]["transaction_type"] = t.transactionType;
      data["transaction"]["amount"] = t.amount;
      data["transaction"]["charges"] = t.charges;
      data["transaction"]["status"] = t.status;

      data["transaction"]["beneficiary_name"] = t.accountName;
      data["transaction"]["beneficiary_bank"] = t.bankName;
      data["transaction"]["beneficiary_account"] = t.accountNumber;
      data["transaction"]["beneficiary_bank_code"] = t.bankCode;
      data["transaction"]["reason"] = t.reason || "";

      data["transaction"]["created_at"] = t.created_at;

      data["wallet"] = {};
      data["wallet"]["id"] = wallet.id;
      data["wallet"]["balance"] = wallet.balance;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async transactionWallet2Wallet(req: Request, res: Response, next: NextFunction) {
    try {
      const { account_number, amount } = req.body;

      if (!account_number) {
        return res.status(400).json({
          success: false,
          message: "Account number is required"
        });
      }

      if (!amount) {
        return res.status(400).json({
          success: false,
          message: "Amount is required"
        });
      }

      const numericAmount = Number(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        return res.status(400).json({
          success: false,
          message: "Amount must be a positive number"
        });
      }

      if (numericAmount < 10) {
        return res.status(400).json({
          success: false,
          message: "Minimum transfer amount is ₦10"
        });
      }

      if (numericAmount > 1000000) {
        return res.status(400).json({
          success: false,
          message: "Maximum transfer amount is ₦1,000,000"
        });
      }

      if (typeof account_number !== 'string' || account_number.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: "Account number must be a valid string"
        });
      }

      if (account_number.length < 10 || account_number.length > 12) {
        return res.status(400).json({
          success: false,
          message: "Account number must be between 10 and 12 characters"
        });
      }

      const walletService = new WalletService();
      const { userId } = res.locals.authenticated_user;

      const transactionWallet2Other = new TransactionWallet2Wallet(
        transactionRepository,
        walletService
      );

      transactionWallet2Other.setUserId(userId);
      transactionWallet2Other.setReceivingAccount(account_number);
      transactionWallet2Other.setAmount(amount);

      const action = await transactionWallet2Other.init();
      console.log(action);

      const { transaction: t, wallet } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      data["transaction"] = {};
      data["transaction"]["id"] = t.id;
      data["transaction"]["transaction_type"] = t.transactionType;
      data["transaction"]["amount"] = t.amount;
      data["transaction"]["charges"] = t.charges;
      data["transaction"]["status"] = t.status;
      data["transaction"]["to"] = t.receivingWallet;
      data["transaction"]["status"] = t.status;
      data["transaction"]["created_at"] = t.created_at;

      data["wallet"] = {};
      data["wallet"]["id"] = wallet.id;
      data["wallet"]["balance"] = wallet.balance;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getTransactionHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const walletService = new WalletService();

      const { userId } = res.locals.authenticated_user;
      const { limit, page, from, to, transaction_type, entry_type } = req.query;

      const transactionViewMany = new TransactionViewMany(transactionRepository, walletService);
      transactionViewMany.setUserId(userId);
      transactionViewMany.setLimit(limit);
      transactionViewMany.setPage(page);

      if (transaction_type) transactionViewMany.setTransactionType(transaction_type.toString());
      if (entry_type) transactionViewMany.setEntryType(entry_type.toString());
      if (from && typeof from === "string") transactionViewMany.setFrom(from.replace(/^"|"$/g, ""));
      if (to && typeof to === "string") transactionViewMany.setTo(to.replace(/^"|"$/g, ""));

      const action = await transactionViewMany.init();

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      const { transactions, limit: plimit, page: ppage, totalCount } = action;

      const transactionsList = transactions.map((transaction: any) => {
        return {
          id: transaction.id,
          entry_type: transaction.entryType,
          amount: transaction.amount,
          transaction_id: transaction.transactionId,
          transaction_type: transaction.transactionType,
          transaction_status: transaction.transactionStatus,
          wallet_id: transaction.walletId,
          payment_method: transaction.paymentMethod,
          statement: transaction.statement,
          timestamp: transaction.updated_at
        };
      });

      const PAGE_LINK = `/transactions?limit=${plimit}&page=`;
      const LAST_PAGE = Math.ceil(totalCount / plimit);

      data["pagination"] = {};
      data["pagination"]["limit"] = plimit;
      data["pagination"]["page"] = ppage;
      data["pagination"]["totalRows"] = totalCount;
      data["pagination"]["next"] = ppage < LAST_PAGE ? `${PAGE_LINK}${ppage + 1}` : null;
      data["pagination"]["prev"] = ppage > 1 ? `${PAGE_LINK}${ppage - 1}` : null;
      data["pagination"]["last"] = `${PAGE_LINK}${LAST_PAGE}`;
      data["pagination"]["first"] = `${PAGE_LINK}1`;
      data["transactions"] = transactionsList;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async getTransaction(req: Request, res: Response, next: NextFunction) {
    try {
      const walletService = new WalletService();

      const { userId } = res.locals.authenticated_user;
      const { id: transactionId } = req.params;

      const transactionView = new TransactionView(transactionRepository, walletService);
      transactionView.setTransactionId(transactionId);

      const action = await transactionView.init();
      if (!action) return res.json({});

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      let data: any = {};

      const { transactionType } = action;

      if (transactionType === "WITHDRAW") data = _tt1(action);
      else if (transactionType === "TRANSFER") data = _tt2(action);
      else if (transactionType === "DEPOSIT") data = _tt3(action);
      else if (transactionType === "AIRTIME" || transactionType === "DATA") data = _tt4(action);

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }
}

function _tt1(t: any): any {
  const data: any = {};

  data["transaction"] = {};
  data["transaction"]["id"] = t.id;
  data["transaction"]["paymentId"] = t.paymentId;
  data["transaction"]["transaction_type"] = t.transactionType;
  data["transaction"]["amount"] = t.amount;
  data["transaction"]["charges"] = t.charges;
  data["transaction"]["status"] = t.status;

  data["transaction"]["beneficiary_name"] = t.bankAccountName;
  data["transaction"]["beneficiary_account"] = t.bankAccountNumber;
  data["transaction"]["beneficiary_bank"] = t.bankName;
  data["transaction"]["reason"] = t.reason || "";
  data["transaction"]["created_at"] = t.created_at;

  return data;
}

function _tt2(t: any): any {
  const data: any = {};

  data["transaction"] = {};
  data["transaction"]["id"] = t.id;
  data["transaction"]["paymentId"] = t.paymentId;
  data["transaction"]["transaction_type"] = t.transactionType;
  data["transaction"]["amount"] = t.amount;
  data["transaction"]["charges"] = t.charges;
  data["transaction"]["status"] = t.status;
  data["transaction"]["to"] = t.receivingWalletId;
  data["transaction"]["reason"] = t.reason || "";
  data["transaction"]["created_at"] = t.created_at;

  return data;
}

function _tt3(t: any): any {
  const data: any = {};

  data["transaction"] = {};
  data["transaction"]["id"] = t.id;
  data["transaction"]["paymentId"] = t.paymentId;
  data["transaction"]["transaction_type"] = t.transactionType;
  data["transaction"]["amount"] = t.amount;
  data["transaction"]["charges"] = t.charges;
  data["transaction"]["status"] = t.status;

  data["transaction"]["to"] = "wallet";
  data["transaction"]["payment_channel"] = t.depositType;
  data["transaction"]["account_name"] = t.bankAccountName;
  data["transaction"]["account_number"] = t.bankAccountNumber;
  data["transaction"]["bank"] = t.bankName;
  data["transaction"]["reason"] = t.reason || "";
  data["transaction"]["created_at"] = t.created_at;

  return data;
}

function _tt4(t: any): any {
  const data: any = {};

  data["transaction"] = {};
  data["transaction"]["id"] = t.id;
  data["transaction"]["transaction_type"] = t.transactionType;
  data["transaction"]["amount"] = t.amount;
  data["transaction"]["charges"] = t.charges;
  data["transaction"]["status"] = t.status;

  if (t.plan) data["transaction"]["plan"] = t.plan;
  data["transaction"]["phone"] = t.phoneNumber;
  data["transaction"]["network"] = t.network;
  data["transaction"]["reference"] = t.reference;

  data["transaction"]["created_at"] = t.created_at;

  return data;
}

export default TransactionController;
