import IWalletRepository from "./IWalletRepository";
import WalletModel from "../../shared/Model/WalletModel";
import Wallet from "./Wallet";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class WalletRepository implements IWalletRepository {
  async createWallet(userId: string): Promise<Wallet> {
    const { id, balance } = await WalletModel.create({ userId: userId });

    return new Wallet(id, balance);
  }

  async getWallet(walletId: string): Promise<Wallet | null> {
    const data: any = await WalletModel.findByPk(walletId);
    if (!data) return null;

    return new Wallet(data.id, data.balance);
  }

  async getUserWallet(userId: string): Promise<Wallet | null> {
    const data: any = await WalletModel.findOne({ where: { userId: userId } });
    if (!data) return null;

    return new Wallet(data.id, data.balance);
  }

  async save(wallet: Wallet) {
    const { id, balance } = wallet.serialize();
    WalletModel.update({ balance: balance }, { where: { id } });
  }

  async setWalletPin(walletId: string, pin: string): Promise<void> {
    WalletModel.update({ pin: pin }, { where: { id: walletId } });
  }
}

export default WalletRepository;
