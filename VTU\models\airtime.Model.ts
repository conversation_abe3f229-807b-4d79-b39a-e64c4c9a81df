import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

export class VTUTransaction extends Model<
  InferAttributes<VTUTransaction>,
  InferCreationAttributes<VTUTransaction>
> {
  declare id: CreationOptional<string>;
  declare userId: string;
  declare transactionId: CreationOptional<string>;
  declare serviceType: string;
  declare phoneNumber: string;
  declare amount: number;
  declare provider: string;
  declare network: string;
  declare status: CreationOptional<string>;
  declare reference: CreationOptional<string>;
  declare providerReference: CreationOptional<string>;
  declare anchorResponse: CreationOptional<string>;
  declare created_at: CreationOptional<Date>;
  declare updated_at: CreationOptional<Date>;
}

VTUTransaction.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING(36),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "User ID cannot be empty"
        },
        len: {
          args: [1, 36],
          msg: "User ID must be between 1 and 36 characters"
        }
      }
    },
    transactionId: {
      field: "transaction_id",
      type: DataTypes.STRING(36),
      allowNull: true,
      validate: {
        len: {
          args: [0, 36],
          msg: "Transaction ID must be between 0 and 36 characters"
        }
      }
    },
    serviceType: {
      field: "service_type",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Service type cannot be empty"
        },
        isIn: {
          args: [['AIRTIME', 'DATA', 'CABLE_TV', 'ELECTRICITY']],
          msg: "Service type must be one of: AIRTIME, DATA, CABLE_TV, ELECTRICITY"
        }
      }
    },
    phoneNumber: {
      field: "phone_number",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Phone number cannot be empty"
        },
        is: {
          args: /^(\+234|234|0)[789][01]\d{8}$/,
          msg: "Please provide a valid Nigerian phone number"
        }
      }
    },
    amount: {
      field: "amount",
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: "Amount must be a valid decimal number"
        },
        min: {
          args: [1],
          msg: "Amount must be greater than 0"
        },
        max: {
          args: [1000000],
          msg: "Amount cannot exceed ₦1,000,000"
        }
      }
    },
    provider: {
      field: "provider",
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "ANCHOR",
      validate: {
        notEmpty: {
          msg: "Provider cannot be empty"
        },
        isIn: {
          args: [['ANCHOR', 'PAYSTACK', 'FLUTTERWAVE']],
          msg: "Provider must be one of: ANCHOR, PAYSTACK, FLUTTERWAVE"
        }
      }
    },
    network: {
      field: "network",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Network cannot be empty"
        },
        isIn: {
          args: [['MTN', 'GLO', 'AIRTEL', '9MOBILE']],
          msg: "Network must be one of: MTN, GLO, AIRTEL, 9MOBILE"
        }
      }
    },
    status: {
      field: "status",
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "PENDING",
      validate: {
        notEmpty: {
          msg: "Status cannot be empty"
        },
        isIn: {
          args: [['PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED']],
          msg: "Status must be one of: PENDING, PROCESSING, SUCCESS, FAILED, CANCELLED"
        }
      }
    },
    reference: {
      field: "reference",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Reference must be between 0 and 100 characters"
        }
      }
    },
    providerReference: {
      field: "provider_reference",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Provider reference must be between 0 and 100 characters"
        }
      }
    },
    anchorResponse: {
      field: "anchor_response",
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 5000],
          msg: "Anchor response must be between 0 and 5000 characters"
        }
      }
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "vtu_transaction",
    tableName: "vtu_transaction",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default VTUTransaction;