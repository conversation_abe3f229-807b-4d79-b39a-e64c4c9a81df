import AppException from "../../AppException";
import IAccountRepository from "./IAccountRepository";
import { domainError } from "../../domainError";

import AnchorModel from "../../shared/Model/AnchorModel";
import AnchorVNModel from "../../shared/Model/AnchorVNModel";
import UserModel from "../../shared/Model/UserModel";
import UserKYCModel from "../../shared/Model/UserKYCModel";

type DeviceInfo = {
  deviceId: string;
  deviceOS: string;
  deviceToken: string;
};

class AccountUpdate {
  private firstname: string;
  private lastname: string;
  private email: string;
  private phone: string;
  private password: string;
  private gender: string;
  private dob: string;
  private bvn: string;

  private deviceInfo: DeviceInfo;

  private account: any;
  private wallet: any;
  private loginSession: any;

  private _repository: IAccountRepository;
  private _bcrypt: any;
  private _walletService: any;

  constructor(AccountRepository: IAccountRepository) {
    this._repository = AccountRepository;
  }

  async init(userId: string, data: any): Promise<any> {
    const user = await UserModel.findByPk(userId);
    const anchor = await AnchorModel.findOne({ where: { userId: userId } });

    if (!user)
      throw new AppException(domainError.TEST_ERROR, "could not update your info at the moment");

    if (!anchor)
      throw new AppException(domainError.TEST_ERROR, "could not update your info at the moment");

    if (anchor.status === "approved")
      throw new AppException(
        domainError.TEST_ERROR,
        "you have passed KYC and cannot update info anymore"
      );

    if (data.phone && data.phone !== user.phone) {
      const isPhoneTaken: boolean = await this._repository.phoneExists(data.phone);
      if (isPhoneTaken)
        throw new AppException(domainError.UNAVAILABLE_PHONE, "provided phone already exists");
    }

    if (data.bvn && data.bvn !== anchor.bvn) {
      const isBVNExisting: boolean = await this._repository.bvnExists(data.bvn);
      if (isBVNExisting)
        throw new AppException(domainError.UNAVAILABLE_PHONE, "provided BVN exists");
    }

    const dData: any = {};
    if (data.firstname) dData.firstname = data.firstname;
    if (data.lastname) dData.lastname = data.lastname;
    if (data.phone) dData.phone = data.phone;
    if (data.gender) dData.gender = data.gender;
    if (data.dob) dData.dob = data.dob;

    await UserModel.update(dData, { where: { id: userId } });

    if (data.bvn) {
      await UserModel.findByPk(userId, { raw: true });

      await AnchorModel.update({ bvn: data.bvn }, { where: { userId: userId } });
      await UserKYCModel.update({ bvn: data.bvn }, { where: { userId: userId } });

      // trigger ancho 2 -- More careful version
    }

    const updatedAccount = await UserModel.findByPk(userId, { raw: true });
    const updatedAnchor = await AnchorModel.findOne({ where: { userId }, raw: true });

    return { account: updatedAccount, anchor: updatedAnchor };
  }
}

export default AccountUpdate;
