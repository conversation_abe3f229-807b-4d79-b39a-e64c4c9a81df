import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransactionWallet2Other extends Model<
  InferAttributes<TransactionWallet2Other>,
  InferCreationAttributes<TransactionWallet2Other>
> {
  declare transactionId: string;
  declare transferRecipientId: string;

  declare anchorPaymentId: String;
  declare anchorPaymentRef: String;

  declare reason: String;

  /*
  bank_account_name: String;
  bank_account_number: String;
  bank_name: String;
  */
}

TransactionWallet2Other.init(
  {
    transactionId: {
      field: "transaction_id",
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      validate: {
        isUUID: {
          args: 4,
          msg: "Transaction ID must be a valid UUID"
        },
        notEmpty: {
          msg: "Transaction ID cannot be empty"
        }
      }
    },
    transferRecipientId: {
      field: "transfer_recipient_id",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 255],
          msg: "Transfer recipient ID must be between 0 and 255 characters"
        }
      }
    },
    anchorPaymentId: {
      field: "anchor_payment_id",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Anchor payment ID must be between 0 and 100 characters"
        },
        isAlphanumeric: {
          msg: "Anchor payment ID must contain only letters and numbers"
        }
      }
    },
    anchorPaymentRef: {
      field: "anchor_payment_ref",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Anchor payment reference must be between 0 and 100 characters"
        }
      }
    },
    reason: {
      field: "reason",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 500],
          msg: "Reason must be between 0 and 500 characters"
        },
        customValidator(value: string) {
          if (value && value.trim().length === 0) {
            throw new Error("Reason cannot be just whitespace");
          }
        }
      }
    }
  },
  {
    sequelize,
    modelName: "transaction_wallet2other",
    tableName: "transaction_wallet2other",
    createdAt: false,
    updatedAt: false
  }
);

export default TransactionWallet2Other;
