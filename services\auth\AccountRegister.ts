import AppException from "../../AppException";
import IAccountRepository from "./IAccountRepository";
import { domainError } from "../../domainError";

import AnchorModel from "../../shared/Model/AnchorModel";
import AnchorVNModel from "../../shared/Model/AnchorVNModel";
import UserModel from "../../shared/Model/UserModel";
import UserKYCModel from "../../shared/Model/UserKYCModel";
import SessionModel from "../../shared/Model/SessionModel";

import AnchorService from "../../AnchorService";
import WalletService from "../../services/wallet/WalletService";

import JWT from "../../libraries/jwt";
import bcrypt from "../../libraries/bcrypt";
import IWalletService from "../../services/wallet/IWalletService";
import generateRandomNigerianAddress from "../../addressgenerator";

type DeviceInfo = {
  deviceId: string;
  deviceOS: string;
  deviceToken: string;
};

class AccountRegister {
  private firstname: string;
  private lastname: string;
  private email: string;
  private phone: string;
  private password: string;
  private gender: string;
  private dob: string;
  private bvn: string;

  private deviceInfo: DeviceInfo;

  private account: any;
  private wallet: any;
  private loginSession: any;

  private _repository: IAccountRepository;
  private _bcrypt: any;
  private _walletService: IWalletService;

  constructor(AccountRepository: IAccountRepository, Bcrypt: any, walletService: IWalletService) {
    this._repository = AccountRepository;
    this._bcrypt = Bcrypt;
    this._walletService = walletService;
  }

  setFirstName(firstname: string): void {
    this.firstname = firstname;
  }

  setLastName(lastname: string): void {
    this.lastname = lastname;
  }

  setEmail(email: string): void {
    this.email = email;
  }

  setPhone(phone: string): void {
    this.phone = phone;
  }

  setGender(gender: string): void {
    this.gender = gender;
  }

  setBVN(bvn: string): void {
    this.bvn = bvn;
  }

  setDOB(dob: string): void {
    this.dob = dob;
  }

  setPassword(password: string): void {
    this.password = password;
  }

  setDeviceInfo(deviceId: string, deviceOS: string, deviceToken: string) {
    this.deviceInfo = { deviceId, deviceOS, deviceToken };
  }

  async init(): Promise<any> {
    const isEmailTaken: boolean = await this._repository.emailExists(this.email);
    const isPhoneTaken: boolean = await this._repository.phoneExists(this.phone);
    const isBVNExisting: boolean = await this._repository.bvnExists(this.bvn);

    if (isEmailTaken)
      throw new AppException(
        domainError.UNAVAILABLE_EMAIL_ADDRESS,
        "provided email is already registered with another account"
      );

    if (isPhoneTaken)
      throw new AppException(
        domainError.UNAVAILABLE_PHONE,
        "provided phone is already registered with another account"
      );

    if (isBVNExisting)
      throw new AppException(
        domainError.UNAVAILABLE_PHONE,
        "provided BVN is already tied to an account"
      );

    const hashedPassword = await bcrypt.hash(this.password, 10);

    const account = await this._repository.createAccount(
      this.firstname,
      this.lastname,
      this.gender,
      this.dob,
      this.email,
      this.phone,
      hashedPassword
    );

    this._repository.saveDeviceInfo(
      account.id,
      this.deviceInfo.deviceId,
      this.deviceInfo.deviceOS,
      this.deviceInfo.deviceToken
    );

    this.account = account;

    const { wallet } = await this._walletService.createWallet(this.account.id);
    const session = await this.issueNewSession();
    let refreshToken: any = await this.issueRefreshToken();

    let deviceInfo: {
      deviceId?: string;
      deviceOS?: string;
      deviceToken?: string;
    } = {};
    if (this.deviceInfo.deviceId) deviceInfo["deviceId"] = this.deviceInfo.deviceId;
    if (this.deviceInfo.deviceId) deviceInfo["deviceOS"] = this.deviceInfo.deviceOS;
    if (this.deviceInfo.deviceId) deviceInfo["deviceToken"] = this.deviceInfo.deviceToken;

    await SessionModel.create({
      userId: account.id,
      sessionToken: session.token,
      refreshToken: refreshToken.token,
      ...deviceInfo
    });

    AccountRegister.SetupKYC(account.id, this.bvn);

    return { account: this.account, wallet: wallet, session: session, refreshToken: refreshToken };
  }

  private async issueNewSession() {
    let session;

    const issued_at: number = new Date().getTime();
    const expire_in: number = 1000 * 3600 * 24;

    const data = {
      id: this.account.id,
      firstname: this.account.firstname,
      lastname: this.account.lastname,
      email: this.account.email,
      phone: this.account.phone
    };

    let payload: any = {};
    payload.issued_at = issued_at;
    payload.expire_in = expire_in;
    payload.data = { userId: this.account.id, ...data };

    const token: string = await JWT.sign(payload);

    session = {
      userId: this.account.id,
      token,
      issued_at,
      expire_in
    };

    return session;
  }

  private async issueRefreshToken() {
    let refresh;

    const issued_at: number = new Date().getTime();
    const expire_in: number = 1000 * 3600 * 24 * 30;

    const data = { id: this.account.id, email: this.account.email };

    let payload: any = {};
    payload.issued_at = issued_at;
    payload.expire_in = expire_in;
    payload.data = { userId: this.account.id, ...data };

    const token: string = await JWT.sign(payload);

    refresh = {
      userId: this.account.id,
      token,
      issued_at,
      expire_in
    };

    return refresh;
  }

  static async SetupKYC(userId: string, bvn: string) {
    let userKYC = await UserKYCModel.findOne({ where: { userId: userId } });

    if (!userKYC) userKYC = await UserKYCModel.create({ userId: userId, bvn: bvn });

    await UserKYCModel.update({ bvn: bvn }, { where: { id: userKYC.id } });
    SetupAnchorUser(userId);
  }
}

async function SetupAnchorUser(userId: string) {
  const userData = await UserModel.findByPk(userId);
  const userKYCData = await UserKYCModel.findOne({ where: { userId: userId } });

  if (!userData) return;
  if (!userKYCData) return;

  // setup anchor
  // create customer
  const anchorCustomer = await AnchorModel.create({ userId: userId });
  console.log("new anchorCustomer", anchorCustomer);

  let fPhoneNumber = userData.phone.replace("+234", "0");

  const customer: any = await AnchorService.CreateCustomer(
    {
      firstName: userData.firstname,
      lastName: userData.lastname,
      email: userData.email,
      phoneNumber: fPhoneNumber
    },
    generateRandomNigerianAddress()
  );

  console.log("##########-CUSTOMER-##########");
  console.log(customer);

  if (!customer?.data?.id) return;
  const customerId = customer.data.id;

  await AnchorModel.update(
    {
      customerId: customerId,
      dateOfBirth: userData.dob,
      bvn: userKYCData.bvn,
      phoneNumber: userData.phone,
      selfieImage: "bxxvxvxbvasbbxvxvx"
    },
    { where: { id: anchorCustomer.id } }
  );
  console.log("customer update", await AnchorModel.findByPk(anchorCustomer.id));

  // verify kyc
  const dobReformat = userData.dob
    .split("-")
    .reverse()
    .join("-");

  const kycVerification = await AnchorService.VerifyKYCLevel2(customerId, {
    dateOfBirth: dobReformat,
    gender: userData.gender,
    bvn: userKYCData.bvn,
    selfieImage: "bxxvxvxbvasbbxvxvx"
  });
  console.log("##########-kycVerification-##########");
  console.log(kycVerification);

  if (!kycVerification?.data?.id) return;
  await AnchorModel.update(
    {
      status: "approved",
      message: "KYC initiated successfully"
    },
    { where: { id: anchorCustomer.id } }
  );
  console.log("kyc update", await AnchorModel.findByPk(anchorCustomer.id));

  /*
  const depositAccount = await AnchorService.CreateDepositAccount(customerId);
  console.log("depositAccount", depositAccount);
  if (!depositAccount.data.id) return;

  await AnchorModel.findByIdAndUpdate(anchorCustomer._id, {
    depositAccountId: depositAccount.data.id,
  });
  console.log("deposit update", await AnchorModel.findById(anchorCustomer._id));
  */
}

/*
async function getUserVerbose(userId: string) {
  const account = await UserModel.findByPk(userId, {
    attributes: {
      exclude: ["password"]
    },
    raw: true
  });
  if (!account) throw new AppException(domainError.NOT_FOUND, "user does not exist");

  let anchor;
  let virtualNuban: any;

  let vn: any;

  const wallet = await walletService.getUserWallet(userId);

  console.log(account);
  console.log(wallet);

  anchor = (await AnchorModel.findOne({ where: { userId: userId }, raw: true })) || {};
  console.log(account);
  console.log(anchor);
  console.log(wallet);
  console.log(
    await AnchorVNModel.findOne({ where: { id: anchor.defaultVirtualNuban }, raw: true }),
    await AnchorVNModel.findOne({
      where: { virtualNubanId: anchor.defaultVirtualNuban },
      raw: true
    })
  );

  vn = anchor?.defaultVirtualNuban
    ? await AnchorVNModel.findOne({
        where: { virtualNubanId: anchor.defaultVirtualNuban }
      })
    : {};
  virtualNuban = vn || {};

  delete virtualNuban?.userId;
  delete virtualNuban?.id;
  delete virtualNuban?.bankId;

  return {
    account: account,
    wallet: { id: wallet.id, balance: wallet.balance },
    // anchor: anchor,
    virtualNuban
  };
}

*/

export default AccountRegister;
