import { Request, Response, NextFunction } from "express";
import { Op } from "sequelize";
import Waitlist from "../../shared/Model/WaitlistModel";
import Mailer from "../../mailer";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class WaitlistController {
  public static async joinWaitlist(req: Request, res: Response, next: NextFunction) {
    try {
      const { firstName, lastName, email } = req.body;

      if (!firstName || !lastName || !email) {
        throw new AppException(
          domainError.INVALID_OR_MISSING_PARAMETER,
          "firstName, lastName, and email are required"
        );
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, "Invalid email format");
      }

      const existingEntry = await Waitlist.findOne({ where: { email } });
      if (existingEntry) {
        throw new AppException(
          domainError.UNAVAILABLE_EMAIL_ADDRESS,
          "Email already exists in waitlist"
        );
      }

      const waitlistEntry = await Waitlist.create({
        firstName,
        lastName,
        email
      });

      await sendWaitlistEmail(firstName, email);

      const response = {
        success: true,
        message: "Successfully joined waitlist",
        data: {
          id: waitlistEntry.id,
          firstName: waitlistEntry.firstName,
          lastName: waitlistEntry.lastName,
          email: waitlistEntry.email,
          beta_tester: waitlistEntry.betaTester,
          created_at: waitlistEntry.created_at
        }
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  }

  public static async getAllWaitlistEntries(req: Request, res: Response, next: NextFunction) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      const { startDate, endDate, search } = req.query;

      const whereClause: any = {};

      if (startDate || endDate) {
        whereClause.created_at = {};
        if (startDate) {
          whereClause.created_at[Op.gte] = new Date(startDate as string);
        }
        if (endDate) {
          whereClause.created_at[Op.lte] = new Date(endDate as string);
        }
      }

      if (search) {
        whereClause[Op.or] = [
          { email: { [Op.like]: `%${search}%` } },
          { firstName: { [Op.like]: `%${search}%` } },
          { lastName: { [Op.like]: `%${search}%` } }
        ];
      }

      const totalCount = await Waitlist.count({ where: whereClause });

      const waitlistEntries = await Waitlist.findAll({
        where: whereClause,
        limit,
        offset,
        order: [["created_at", "DESC"]],
        attributes: ["id", "firstName", "lastName", "email", "created_at", "updated_at"]
      });

      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const response = {
        success: true,
        message: "Waitlist entries retrieved successfully",
        data: {
          entries: waitlistEntries,
          pagination: {
            currentPage: page,
            totalPages,
            totalCount,
            limit,
            hasNextPage,
            hasPrevPage
          }
        }
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  public static async removeFromWaitlist(req: Request, res: Response, next: NextFunction) {
    try {
      const { email } = req.body;

      if (!email) {
        throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, "Email is required");
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppException(domainError.INVALID_OR_MISSING_PARAMETER, "Invalid email format");
      }

      const waitlistEntry = await Waitlist.findOne({ where: { email } });

      if (!waitlistEntry) {
        throw new AppException(domainError.NOT_FOUND, "Email not found in waitlist");
      }

      const firstName = waitlistEntry.firstName;
      const userEmail = waitlistEntry.email;

      await waitlistEntry.destroy();

      await sendOptOutEmail(firstName, userEmail);

      const response = {
        success: true,
        message: "Successfully removed from waitlist",
        data: {
          email: userEmail,
          removedAt: new Date().toISOString()
        }
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
}

async function sendWaitlistEmail(firstName: string, email: string) {
  const subject = "Welcome to Agentpesa Waitlist!";
    const body = `
   <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Welcome to Agentpesa</title>
    <style>
        /* Basic resets and font styles */
        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }
        a {
            color: inherit;
            text-decoration: none;
        }

        
        /* Responsive styles for mobile */
        @media screen and (max-width: 600px) {
            .content-table {
                width: 100% !important;
            }
            .header-text {
                font-size: 28px !important;
            }
            .body-text {
               font-size: 16px !important;
               line-height: 1.5 !important;
            }

        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <!-- Main Content Table -->
                <table align="center" border="0" cellpadding="0" cellspacing="0" class="content-table" style="width: 600px; max-width: 600px; border-spacing: 0; background-color: #ffffff; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); border-radius: 8px; overflow: hidden;">
                    
                    <!-- Header Section -->
                    <tr>
                        <td align="center" style="padding: 40px 0 32px 0;">
                            <h1 class="header-text" style="margin: 0; color: #1f2937; font-size: 36px; font-weight: bold; letter-spacing: 1px;">
                                Agentpesa
                            </h1>
                        </td>
                    </tr>

                    <!-- Content Section -->
                    <tr>
                        <td style="padding: 0 40px 40px 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #1f2937; font-size: 28px; font-weight: bold;">
                                Welcome to the Waitlist, ${firstName}!
                            </h2>
                            <p class="body-text" style="margin: 0 0 20px 0; color: #4b5563; font-size: 18px; line-height: 1.6;">
                                Thank you for joining the Agentpesa waitlist! We're thrilled to have you on board and can't wait to share what we've been building.
                            </p>
                            <p class="body-text" style="margin: 0 0 32px 0; color: #4b5563; font-size: 18px; line-height: 1.6;">
                                You'll be among the first to know when Agentpesa launches. We're putting the finishing touches on an experience designed to simplify your financial life.
                            </p>

                            <!-- "What's Next" Callout Box -->
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 32px 0;">
                                <tr>
                                    <td style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 24px; border-radius: 0 8px 8px 0;">
                                        <p style="margin: 0 0 12px 0; font-weight: bold; color: #1e40af; font-size: 18px;">
                                            🎉 What's next?
                                        </p>
                                        <p style="margin: 0; color: #1d4ed8; font-size: 16px; line-height: 1.5;">
                                            We'll send you an email notification the moment Agentpesa is ready for you. Keep an eye on your inbox for updates and exclusive early access!
                                        </p>
                                    </td>
                                </tr>
                            </table>
                            
                            <p class="body-text" style="margin: 0; color: #4b5563; font-size: 18px; line-height: 1.6;">
                                Stay tuned and get ready to transform your financial experience!
                            </p>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #f9fafb; padding: 32px 40px; border-top: 1px solid #e5e7eb;">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <!-- Signature -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 14px; line-height: 1.5;">
                                            Thank you,<br>
                                            <strong style="color: #374151;">The Agentpesa Team</strong>
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Social Media Links -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">
                                            Follow us on social media:
                                        </p>
                                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                            <tr>
                                                <!-- Facebook -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.facebook.com/61577778871615/posts/122121339824925962/?mibextid=rS40aB7S9Ucbxw6v" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #1877f2; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/color/48/facebook-new.png" alt="Facebook" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- X (Twitter) -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://x.com/agentpesa/status/1953764937261556068?t=U7zYPPac6SewLQhObQr6qQ&s=19" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #000000; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/color/48/twitterx--v2.png" alt="Twitter" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- Instagram -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.instagram.com/p/DNFzKrpCT-c/?igsh=NTc4MTIwNjQ2YQ==" target="_blank" style="display: inline-block; width: 32px; height: 32px; background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/color/48/instagram-new--v1.png" alt="Instagram" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                                <!-- LinkedIn -->
                                                <td style="padding: 0 8px;">
                                                    <a href="https://www.linkedin.com/posts/agentpesa_agentpesa-weekendfun-digitalpayments-activity-7359532138024595456-DOyQ?utm_source=share&utm_medium=member_desktop&rcm=ACoAACviJ0YBTXuW25FMgX-il9nsczv7hW8S72Q" target="_blank" style="display: inline-block; width: 32px; height: 32px; background-color: #0077b5; border-radius: 6px; text-align: center; line-height: 32px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/color/48/linkedin.png" alt="LinkedIn" width="20" height="20" style="display: block; margin: 6px auto; border: 0;">
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <!-- Optional: Unsubscribe link -->
                                <tr>
                                    <td align="center" style="padding-top: 24px;">
                                        <p style="margin: 0; color: #9ca3af; font-size: 12px;">
                                            You're receiving this because you signed up for our waitlist.<br>
                                            <a href="#" style="color: #6b7280; text-decoration: underline;">Manage preferences</a>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `;

  await Mailer.send(email, subject, body);
}

async function sendOptOutEmail(firstName: string, email: string) {
  const subject = "You've been removed from Agentpesa Waitlist";
  const body = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c3e50;">Agentpesa</h1>
      </div>

      <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">Goodbye for now, ${firstName}!</h2>

        <p style="color: #555; font-size: 16px; line-height: 1.6;">
          You have been successfully removed from the Agentpesa waitlist as requested.
        </p>

        <p style="color: #555; font-size: 16px; line-height: 1.6;">
          We're sorry to see you go! If you change your mind, you can always join our waitlist
          again by visiting our website.
        </p>

        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <p style="color: #2980b9; font-weight: bold; margin: 0;">
            📧 Email Removed
          </p>
          <p style="color: #2980b9; margin: 10px 0 0 0;">
            Your email address (${email}) has been completely removed from our waitlist database.
          </p>
        </div>

        <p style="color: #555; font-size: 16px; line-height: 1.6;">
          Thank you for your interest in Agentpesa. We hope to serve you in the future!
        </p>
      </div>

      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #888; font-size: 14px;">
          Best regards,<br>
          <strong>The Agentpesa Team</strong>
        </p>
      </div>
    </div>
  `;

  await Mailer.send(email, subject, body);
}

export default WaitlistController;
