import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class Session extends Model<InferAttributes<Session>, InferCreationAttributes<Session>> {
  declare id: CreationOptional<string>;
  declare userId: string;

  declare sessionToken: string;
  declare refreshToken: string;

  declare deviceId: CreationOptional<string>;
  declare deviceOS: CreationOptional<string>;
  declare deviceToken: CreationOptional<string>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Session.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    sessionToken: {
      field: "session_token",
      type: DataTypes.STRING
    },
    refreshToken: {
      field: "refresh_token",
      type: DataTypes.STRING
    },

    deviceId: {
      field: "device_id",
      type: DataTypes.STRING
    },
    deviceOS: {
      field: "device_os",
      type: DataTypes.STRING
    },
    deviceToken: {
      field: "device_token",
      type: DataTypes.STRING
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "session",
    tableName: "session",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Session;
