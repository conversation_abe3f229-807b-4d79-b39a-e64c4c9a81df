import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class OTP extends Model<InferAttributes<OTP>, InferCreationAttributes<OTP>> {
  declare otp_id: CreationOptional<string>;
  declare otp_code: string;
  declare data: string;
  declare generated_time: number;
  declare expiration_time: number;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

OTP.init(
  {
    otp_id: {
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    otp_code: {
      type: DataTypes.STRING
    },
    data: {
      type: DataTypes.STRING
    },
    generated_time: {
      type: DataTypes.NUMBER
    },
    expiration_time: {
      type: DataTypes.NUMBER
    },

    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "otp",
    tableName: "otp",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default OTP;
