import Transaction from "./Transaction";
import ITransactionRepository from "./ITransactionRepository";
import IWalletService from "../wallet/IWalletService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import WalletModel from "../../shared/Model/WalletModel";
import AnchorModel from "../../shared/Model/AnchorModel";
import UserModel from "../../shared/Model/UserModel";
import AnchorVNModel from "../../shared/Model/AnchorVNModel";
import AnchorService from "../../AnchorService";

class TransactionWallet2Wallet extends Transaction {
  private userId: string;

  private receivingWallet: string;
  private receivingAccount: string;

  private initiatorWallet: any;
  private receiverWallet: any;

  private _repository: ITransactionRepository;
  private _walletService: IWalletService;

  constructor(transactionRepository: ITransactionRepository, walletService: IWalletService) {
    super();

    this._repository = transactionRepository;
    this._walletService = walletService;
    this.transactionType = "TRANSFER";
    this.charges = 0;
  }

  public setInitiatorWallet(walletId: string) {
    this.initiatingWallet = walletId;
  }
  public setReceivingWallet(walletId: string) {
    this.receivingWallet = walletId;
  }
  public setReceivingAccount(accountNumber: string) {
    this.receivingAccount = accountNumber;
  }
  public setAmount(amount: number) {
    this.amount = amount;
  }
  public setUserId(userId: string) {
    this.userId = userId;
  }

  async createTransfer(): Promise<any> {}

  public async init() {
    let initiatorWallet: any = (await this._walletService.getUserWallet(this.userId))?.wallet;
    if (!initiatorWallet) throw new AppException(domainError.WALLET_AUTHORIZATION_ERROR);
    this.initiatingWallet = initiatorWallet.id;

    const total = this.amount + this.charges;

    // check anchor
    const senderWallet = await WalletModel.findByPk(this.initiatingWallet);
    const senderAnchor = await AnchorModel.findOne({ where: { userId: senderWallet?.userId } });
    if (!senderAnchor?.depositAccountId)
      throw new AppException(
        domainError.TEST_ERROR,
        "your account setup is not complete and can not run transaction"
      ); // cannot send money

    const recieverVN = await AnchorVNModel.findOne({
      where: { accountNumber: this.receivingAccount }
    });
    if (!recieverVN) throw new AppException(domainError.TEST_ERROR, "account does not exist"); // cannot receive money

    const recieverUser = await UserModel.findOne({ where: { id: recieverVN.userId } });
    if (recieverUser?.deactivated)
      throw new AppException(domainError.TEST_ERROR, "invalid receiver account");

    const recieverAnchor = await AnchorModel.findOne({
      where: { defaultVirtualNuban: recieverVN.virtualNubanId }
    });
    if (!recieverAnchor?.depositAccountId)
      throw new AppException(
        domainError.TEST_ERROR,
        "the account you are sending to cannot recieve money at the moment 2"
      ); // cannot receive money

    const recieverWallet = await WalletModel.findOne({ where: { userId: recieverAnchor.userId } });
    if (!recieverWallet)
      throw new AppException(domainError.TEST_ERROR, "could not complete transfer at the moment");

    this.receivingWallet = recieverWallet.id;

    if (initiatorWallet.id === this.receivingWallet)
      throw new AppException(domainError.TEST_ERROR, "you cannot transfer to yourself");

    // return {
    //   transaction: {},
    //   wallet: {},
    //   receiverWallet: {}
    // };

    /*
    const recieverWallet = await WalletModel.findByPk(this.receivingWallet);
    const recieverAnchor = await AnchorModel.findOne({ where: { userId: recieverWallet?.userId } });
    if (!recieverAnchor?.depositAccountId)
      throw new AppException(
        domainError.TEST_ERROR,
        "the account you are sending to cannot recieve money at the moment"
      ); // cannot receive money
      */

    // check wallet balance
    if (total > initiatorWallet.balance)
      throw new AppException(domainError.TEST_ERROR, "Insufficient Balace");

    // initiate anchor book transfer
    const anchorTransfer = await AnchorService.initiateBookTransfer(
      total * 100,
      senderAnchor.depositAccountId,
      recieverAnchor.depositAccountId
    );

    console.log(anchorTransfer);

    if (!anchorTransfer?.data)
      throw new AppException(domainError.TEST_ERROR, "could not complete transfer at the moment");

    const anchorPaymentId = anchorTransfer.data.id;
    const anchorPaymentRef = anchorTransfer.data.attributes.reference;
    const anchorTransferStatus = anchorTransfer.data.attributes.status;

    if (anchorTransferStatus === "FAILED")
      throw new AppException(domainError.TEST_ERROR, "could not complete transfer at the moment");

    this.status = anchorTransferStatus;

    console.log("HERE HERE HERE HERE");

    this.initiatorWallet = (
      await this._walletService.withdraw(this.initiatingWallet, total)
    ).wallet;
    this.receiverWallet = (
      await this._walletService.deposit(this.receivingWallet, this.amount)
    ).wallet;

    // create transaction
    const transaction = await this._repository.createTransactionWallet2Wallet({
      transactionType: this.transactionType,
      amount: this.amount,
      charges: this.charges,
      initiatingWalletId: this.initiatingWallet,
      receivingWallet: this.receivingWallet,
      status: anchorTransferStatus,
      userId: this.userId,
      anchorPaymentId: anchorPaymentId,
      anchorPaymentRef: anchorPaymentRef,
      reason: ""
    });
    this.id = transaction.id;
    this.created_at = transaction.created_at;

    await this._repository.writeLog({
      transactionId: transaction.transactionId,
      walletId: this.initiatingWallet,
      entryType: "DEBIT",
      amount: this.amount,
      transactionStatus: this.status,
      statement: "transfer to wallet",
      paymentMethod: "WALLET",
      transactionType: "TRANSFER",
      walletBalance: this.initiatorWallet.balance
    });

    await this._repository.writeLog({
      transactionId: transaction.transactionId,
      walletId: this.receivingWallet,
      entryType: "CREDIT",
      amount: this.amount,
      transactionStatus: this.status,
      statement: "deposit from wallet",
      paymentMethod: "WALLET",
      transactionType: "TRANSFER",
      walletBalance: this.receiverWallet.balance
    });

    console.log(this);

    return {
      transaction: this.serialize(),
      wallet: this.initiatorWallet,
      receiverWallet: this.receiverWallet
    };
  }

  serialize(): any {
    return {
      id: this.id,
      transactionType: this.transactionType,
      amount: this.amount,
      charges: this.charges,
      status: this.status,
      initiatingWallet: this.initiatingWallet,
      receivingWallet: this.receivingWallet
    };
  }
}

export default TransactionWallet2Wallet;

/*

TransactionWallet2Wallet {
  id: '0a6b3bd3-dcad-4be1-890c-d778dc59a109',
  transactionType: 'TRANSFER',
  amount: 2500,
  charges: 0,
  status: 'PENDING',
  initiatingWallet: 'f51a08b3-d0f1-478a-9cff-74e020ee4cd2',
  created_at: undefined,
  userId: '0d45d7bf-0f92-497b-aa5e-601e73b7ee08',
  receivingWallet: '9262f9a3-0c16-41da-8da0-a776d1a3a729',
  initiatorWallet: undefined,
  receiverWallet: Wallet { id: '9262f9a3-0c16-41da-8da0-a776d1a3a729', balance: 5000 },
  _repository: TransactionRepository {},
  _walletService: WalletService {}
}
{
  transaction: {
    id: '0a6b3bd3-dcad-4be1-890c-d778dc59a109',
    transactionType: 'TRANSFER',
    amount: 2500,
    status: 'PENDING',
    initiatingWallet: 'f51a08b3-d0f1-478a-9cff-74e020ee4cd2',
    receivingWallet: '9262f9a3-0c16-41da-8da0-a776d1a3a729'
  },
  wallet: undefined,
  receiverWallet: Wallet { id: '9262f9a3-0c16-41da-8da0-a776d1a3a729', balance: 5000 }
}

*/
