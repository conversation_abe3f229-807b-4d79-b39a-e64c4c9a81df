import sgMail from "@sendgrid/mail";

const SENDGRID_API_KEY: any = process.env.SENDGRID_API_KEY;
const MAIL_TRANSPORT_USER: string = process.env.MAIL_TRANSPORT_USER || "";

sgMail.setApiKey(SENDGRID_API_KEY);

if (!MAIL_TRANSPORT_USER) throw new Error("Undefined MAIL_TRANSPORT_USER");

async function send(to: string, subject: string, body: string) {
  const mail = {
    from: MAIL_TRANSPORT_USER,
    to: to,
    subject: subject,
    html: body,
  };

  const mailInfo = await sgMail.send(mail);
  console.log(mailInfo);
}

export default { send };
