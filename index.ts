import dotenv from "dotenv";
dotenv.config();

import config from "config";
import express, { Express, NextFunction } from "express";
import cookieParser from "cookie-parser";
import bodyParser from "body-parser";
import cors from "cors";
import router from "./app/router";
import { stderrStream, stdoutStream } from "./app/utils/logger/morgan";
import db from "./app/db";
import {
  errorDecorator,
  finalError<PERSON>and<PERSON>,
  notFound<PERSON>rror<PERSON><PERSON><PERSON>,
  unhandledRejectionHandler,
  uncaughtExceptionHandler
} from "./app/utils/errorMiddleware";

class Application {
  private static _server: Express;

  public static async startServer(): Promise<void> {
    this._server = express();
    this._server.set("env", process.env.NODE_ENV);
    this._server.set("host", config.get("app.host"));
    this._server.set("port", process.env.PORT);
    this._server.set("trust proxy", true);

    this._server.set("view engine", "ejs");

    await this.connectDB();

    // Middlewares
    this._server.use(stderrStream, stdoutStream);
    this._server.use(bodyParser.json());
    this._server.use(bodyParser.urlencoded({ extended: true }));
    this._server.use(cookieParser());
    this._server.use(cors());

    this._server.use(router);

    // monitor memory
    this._server.use((req, res, next) => {
      const memoryUsage = process.memoryUsage();
      const toMB = (bytes: number) => (bytes / (1024 * 1024)).toFixed(2);

      console.log(`___________________________`);
      console.log(`RSS: ${toMB(memoryUsage.rss)} MB`);
      console.log(`Heap Total: ${toMB(memoryUsage.heapTotal)} MB`);
      console.log(`Heap Used: ${toMB(memoryUsage.heapUsed)} MB`);
      console.log(`External: ${toMB(memoryUsage.external)} MB`);
      console.log(`ArrayBuffers: ${toMB(memoryUsage.arrayBuffers)} MB`);
      console.log(`___________________________`);

      next();
    });

    this._server.use((req, res, next) => {
      res.on("finish", () => {
        const memoryUsage = process.memoryUsage();
        const toMB = (bytes: number) => (bytes / (1024 * 1024)).toFixed(2);

        console.log(`___________________________`);
        console.log(`RSS: ${toMB(memoryUsage.rss)} MB`);
        console.log(`Heap Total: ${toMB(memoryUsage.heapTotal)} MB`);
        console.log(`Heap Used: ${toMB(memoryUsage.heapUsed)} MB`);
        console.log(`External: ${toMB(memoryUsage.external)} MB`);
        console.log(`ArrayBuffers: ${toMB(memoryUsage.arrayBuffers)} MB`);
        console.log(`___________________________`);
      });
      next();
    });

    // Error handling
    process.on("unhandledRejection", unhandledRejectionHandler);
    process.on("uncaughtException", uncaughtExceptionHandler);
    process.on("SIGTERM", () => {
      process.exit(0);
    });

    this._server.use(notFoundErrorHandler);
    this._server.use(errorDecorator);
    this._server.use(finalErrorHandler);

    const host: string = this._server.get("host");
    const port: number = this._server.get("port");
    this._server.listen(port, host, () => {
      console.log(`Server started at http://${config.get("app.host")}:${config.get("app.port")}`);
    });
  }

  public static async connectDB(): Promise<void> {
    db();
  }
}

Application.startServer();
