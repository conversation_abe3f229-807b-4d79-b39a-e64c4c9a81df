import IBeneficiaryRepository from "./IBeneficiaryRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class SearchBeneficiary {
  private _repository: IBeneficiaryRepository;

  constructor(beneficiaryRepository: IBeneficiaryRepository) {
    this._repository = beneficiaryRepository;
  }

  async init(aliasString: string): Promise<any> {
    const beneficiaries = await this._repository.searchBeneficiary(aliasString);
    console.log(beneficiaries);

    return beneficiaries;
  }
}

export default SearchBeneficiary;
