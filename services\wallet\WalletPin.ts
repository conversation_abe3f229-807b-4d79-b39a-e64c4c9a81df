import AppException from "../../AppException";
import { domainError } from "../../domainError";
import WalletModel from "../../shared/Model/WalletModel";

import bcrypt from "../../libraries/bcrypt";
import IWalletRepository from "./IWalletRepository";

class WalletPin {
  private _walletRepository: IWalletRepository;

  constructor(walletRepository: IWalletRepository) {
    this._walletRepository = walletRepository;
  }

  async init(userId: string, pin: string) {
    let wallet = await this._walletRepository.getUserWallet(userId);
    if (!wallet) throw new AppException(domainError.WALLET_AUTHORIZATION_ERROR);

    const hashedPin = await bcrypt.hash(pin, 10);
    await this._walletRepository.setWalletPin(wallet.getId(), hashedPin);

    return { wallet };
  }
}

export default WalletPin;
