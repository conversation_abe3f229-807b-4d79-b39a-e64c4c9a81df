import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class SavedPhone extends Model<InferAttributes<SavedPhone>, InferCreationAttributes<SavedPhone>> {
  declare id: CreationOptional<string>;
  declare userId: string;

  declare phone: string;
  declare alias: string;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

SavedPhone.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    phone: {
      field: "phone",
      type: DataTypes.STRING
    },
    alias: {
      field: "alias",
      type: DataTypes.STRING
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user_saved_phone",
    tableName: "user_saved_phone",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default SavedPhone;
