import IBeneficiaryRepository from "./IBeneficiaryRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class GetBeneficiary {
  private _repository: IBeneficiaryRepository;

  constructor(beneficiaryRepository: IBeneficiaryRepository) {
    this._repository = beneficiaryRepository;
  }

  async init(userId: string, beneficiaryId: string): Promise<any> {
    const beneficiary = await this._repository.getBeneficiary(userId, beneficiaryId);
    if (!beneficiary) throw new AppException(domainError.NOT_FOUND, "no beneficiary found");

    return { beneficiary };
  }
}

export default GetBeneficiary;
