import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransactionVTU extends Model<
  InferAttributes<TransactionVTU>,
  InferCreationAttributes<TransactionVTU>
> {
  declare transactionId: string;
  declare phoneNumber: string;
  declare provider: CreationOptional<string>;
  declare network: string;
  declare reference: string;
  declare plan: CreationOptional<string>;
  declare providerReference: CreationOptional<string>;
  declare anchorResponse: CreationOptional<string>;
}

TransactionVTU.init(
  {
    transactionId: {
      field: "transaction_id",
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      validate: {
        isUUID: {
          args: 4,
          msg: "Transaction ID must be a valid UUID"
        },
        notEmpty: {
          msg: "Transaction ID cannot be empty"
        }
      }
    },
    phoneNumber: {
      field: "phone_number",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Phone number cannot be empty"
        },
        is: {
          args: /^(\+234|234|0)[789][01]\d{8}$/,
          msg: "Please provide a valid Nigerian phone number"
        }
      }
    },
    provider: {
      field: "provider",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isIn: {
          args: [['ANCHOR', 'PAYSTACK', 'FLUTTERWAVE']],
          msg: "Provider must be one of: ANCHOR, PAYSTACK, FLUTTERWAVE"
        }
      }
    },
    network: {
      field: "network",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Network cannot be empty"
        },
        isIn: {
          args: [['MTN', 'GLO', 'AIRTEL', '9MOBILE']],
          msg: "Network must be one of: MTN, GLO, AIRTEL, 9MOBILE"
        }
      }
    },
    reference: {
      field: "reference",
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Reference cannot be empty"
        },
        len: {
          args: [1, 100],
          msg: "Reference must be between 1 and 100 characters"
        }
      }
    },
    plan: {
      field: "plan",
      type: DataTypes.STRING
    },
    providerReference: {
      field: "provider_reference",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: "Provider reference must be between 0 and 100 characters"
        }
      }
    },
    anchorResponse: {
      field: "anchor_response",
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 5000],
          msg: "Anchor response must be between 0 and 5000 characters"
        }
      }
    }
  },
  {
    sequelize,
    modelName: "transaction_vtu",
    tableName: "transaction_vtu",
    createdAt: false,
    updatedAt: false
  }
);

export default TransactionVTU;
