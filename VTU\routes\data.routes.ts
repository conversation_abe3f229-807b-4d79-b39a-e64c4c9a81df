import { Router } from 'express';
import { DataController } from '../controllers/data.controller';
import AuthProtectionMiddleware from '../../AuthProtectionMiddleware'; 

const router = Router();
const dataController = new DataController();

// Data purchase routes (protected)
router.post('/purchase', AuthProtectionMiddleware, dataController.purchaseData.bind(dataController));

// Utility routes
router.get('/plans', dataController.getDataPlans.bind(dataController));
router.get('/billers', dataController.getDataBillers.bind(dataController));

export default router;