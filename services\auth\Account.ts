class Account {
  public id: string;
  public firstname: string;
  public lastname: string;
  public email: string;
  public phone: string;
  public gender: string;
  public dob: string;
  public password: string;
  public created_at: string;

  constructor(
    id: string,
    firstname: string,
    lastname: string,
    email: string,
    phone: string,
    gender: string,
    dob: string,
    password: string,
    created_at: string
  ) {
    this.id = id;
    this.firstname = firstname;
    this.lastname = lastname;
    this.email = email;
    this.phone = phone;
    this.gender = gender;
    this.dob = dob;
    this.password = password;
    this.created_at = created_at;
  }

  getId() {
    return this.id;
  }

  getEmail() {
    return this.email;
  }
}

export default Account;
