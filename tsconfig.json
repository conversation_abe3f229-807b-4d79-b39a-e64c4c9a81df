{
  "compilerOptions": {
    "target": "es2022",
    "module": "commonjs",
    "strict": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "moduleResolution": "node",
    "noImplicitAny": true,
    "sourceMap": true,
    // "outDir": "",
    "baseUrl": ".",

    "forceConsistentCasingInFileNames": true,
    "strictPropertyInitialization": false,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    // "noEmitOnError": true,
    "paths": {
      "*": ["node_modules/*"]
    }
  },
  "include": ["./**/*"],
  "exclude": ["node_modules"]
}
