import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class Waitlist extends Model<InferAttributes<Waitlist>, InferCreationAttributes<Waitlist>> {
  declare id: CreationOptional<string>;
  declare firstName: string;
  declare lastName: string;
  declare email: string;
  declare betaTester: CreationOptional<boolean>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Waitlist.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    firstName: {
      field: "first_name",
      type: DataTypes.STRING,
      allowNull: false
    },
    lastName: {
      field: "last_name",
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    betaTester: {
      field: "beta_tester",
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "waitlist",
    tableName: "waitlist",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Waitlist;
