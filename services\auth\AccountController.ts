import { Request, Response, NextFunction } from "express";
import AccountPhoneVerification from "./AccountPhoneVerification";
import AccountEmailVerification from "./AccountEmailVerification";
import AccountRegister from "./AccountRegister";
import AccountUpdate from "./AccountUpdate";
import AccountView from "./AccountView";
import AccountDelete from "./AccountDelete";
import AccountRestore from "./AccountRestore";
import AccountLogin from "./AccountLogin";
import AccountPasswordReset from "./AccountPasswordReset";

import AccountRepository from "./AccountRepository";
import AccountResponseFormat from "./AccountResponseFormat";
import AccountValidation from "./AccountValidation";
import Bcrypt from "../../libraries/bcrypt";
import UUID from "../../libraries/uuid";

import WalletService from "../../services/wallet/WalletService";

import AccountPFP from "./AccountPFP";

const accountRepository = new AccountRepository();
const walletService = new WalletService();

class AccountController {
  public static async RequestPhoneVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { phone } = await AccountValidation.requestPhoneVerification(req.body);

      const action = await AccountPhoneVerification.RequestPhoneVerification(phone);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "OK";
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["code"] = action.otpCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async ConfirmPhoneVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { otp_id, otp_code } = req.body;
      // await apiValidation.ConfirmVerification(req.body);

      const action = await AccountPhoneVerification.ConfirmPhoneVerification(otp_id, otp_code);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "email verification successful";
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["token"] = action.token;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async RequestEmailVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { email } = await AccountValidation.requestEmailVerification(req.body);

      const action = await AccountEmailVerification.RequestEmailVerification(email);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "OK";
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["code"] = action.otpCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async ConfirmEmailVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { otp_id, otp_code } = req.body;

      const action = await AccountEmailVerification.ConfirmEmailVerification(otp_id, otp_code);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "email verification successful";
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["token"] = action.token;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async CreateAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        firstname,
        lastname,
        email,
        phone,
        gender,
        dob,
        bvn,
        password,
        device_id,
        device_os,
        device_token
      } = await AccountValidation.createAccount(req.body);

      const accountRegister = new AccountRegister(accountRepository, Bcrypt, walletService);
      accountRegister.setFirstName(firstname);
      accountRegister.setLastName(lastname);
      accountRegister.setEmail(email);
      accountRegister.setPhone(phone);
      accountRegister.setGender(gender);
      accountRegister.setDOB(dob);
      accountRegister.setBVN(bvn);
      accountRegister.setPassword(password);

      accountRegister.setDeviceInfo(device_id, device_os, device_token);

      const action = await accountRegister.init();

      const { account, session, refreshToken } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      const accountData: any = {};
      accountData["id"] = account.id;
      accountData["firstname"] = account.firstname;
      accountData["lastname"] = account.lastname;
      accountData["email"] = account.email;
      accountData["phone"] = account.phone;
      accountData["gender"] = account.gender;
      accountData["dob"] = account.dob;

      console.log(session);

      const sessionData: any = {};
      sessionData["user_id"] = session.userId;
      sessionData["token"] = session.token;
      sessionData["issued_at"] = session.issued_at;
      sessionData["expire_in"] = session.expire_in;
      sessionData["refresh"] = {};
      sessionData["refresh"]["token"] = refreshToken.token;
      sessionData["refresh"]["issued_at"] = refreshToken.issued_at;
      sessionData["refresh"]["expire_in"] = refreshToken.expire_in;

      data["session"] = sessionData;
      data["account"] = accountData;

      const walletData: any = {};
      walletData["id"] = action.wallet.id;
      walletData["balance"] = action.wallet.balance;

      data["account"] = accountData;
      data["wallet"] = walletData;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async LoginAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        email,
        password,
        device_id,
        device_os,
        device_token
      } = await AccountValidation.loginAccount(req.body);

      const accountLogin = new AccountLogin(accountRepository);
      accountLogin.setLoginId(email);
      accountLogin.setPassword(password);
      accountLogin.setDeviceInfo(device_id, device_os, device_token);

      const action = await accountLogin.init();

      const { account, session, refreshToken, usv } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "sign in successful";
      const data: any = {};

      const accountData: any = {};
      accountData["id"] = account.id;
      accountData["firstname"] = account.firstname;
      accountData["lastname"] = account.lastname;
      accountData["email"] = account.email;
      accountData["phone"] = account.phone;
      accountData["gender"] = account.gender;
      accountData["dob"] = account.dob;
      accountData["kyc_verified"] = usv.isKYCVerified;

      const sessionData: any = {};
      sessionData["user_id"] = session.userId;
      sessionData["token"] = session.token;
      sessionData["issued_at"] = session.issued_at;
      sessionData["expire_in"] = session.expire_in;
      sessionData["refresh"] = {};
      sessionData["refresh"]["token"] = refreshToken.token;
      sessionData["refresh"]["issued_at"] = refreshToken.issued_at;
      sessionData["refresh"]["expire_in"] = refreshToken.expire_in;

      data["session"] = sessionData;
      data["account"] = accountData;

      const walletData: any = {};
      walletData["id"] = usv.wallet.id;
      walletData["balance"] = usv.wallet.balance;

      data["account"] = accountData;
      data["wallet"] = walletData;
      data["virtualNuban"] = usv.virtualNuban;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async RefreshToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { token } = req.body;

      const accountLogin = new AccountLogin(accountRepository);
      const action = await accountLogin.refresh(token);
      const { account, session, refreshToken, usv } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "sign in successful";
      const data: any = {};

      const accountData: any = {};
      accountData["id"] = account.id;
      accountData["firstname"] = account.firstname;
      accountData["lastname"] = account.lastname;
      accountData["email"] = account.email;
      accountData["phone"] = account.phone;
      accountData["gender"] = account.gender;
      accountData["dob"] = account.dob;
      accountData["kyc_verified"] = usv.isKYCVerified;

      const sessionData: any = {};
      sessionData["user_id"] = session.userId;
      sessionData["token"] = session.token;
      sessionData["issued_at"] = session.issued_at;
      sessionData["expire_in"] = session.expire_in;
      sessionData["refresh"] = {};
      sessionData["refresh"]["token"] = refreshToken.token;
      sessionData["refresh"]["issued_at"] = refreshToken.issued_at;
      sessionData["refresh"]["expire_in"] = refreshToken.expire_in;

      data["session"] = sessionData;
      data["account"] = accountData;

      const walletData: any = {};
      walletData["id"] = usv.wallet.id;
      walletData["balance"] = usv.wallet.balance;

      data["account"] = accountData;
      data["wallet"] = walletData;
      data["virtualNuban"] = usv.virtualNuban;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async UpdateDeviceToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { token } = req.body;

      const accountLogin = new AccountLogin(accountRepository);
      const action = await accountLogin.updateDeviceToken(userId, token);
      console.log(action);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async ViewAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;

      const action = await AccountView(userId);
      console.log(action);

      const { account, wallet } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      const accountData: any = {};
      accountData["id"] = account.id;
      accountData["firstname"] = account.firstname;
      accountData["lastname"] = account.lastname;
      accountData["email"] = account.email;
      accountData["phone"] = account.phone;
      accountData["gender"] = account.gender;
      accountData["dob"] = account.dob;
      accountData["kyc_verified"] = action.isKYCVerified;

      const walletData: any = {};
      walletData["id"] = wallet.id;
      walletData["balance"] = wallet.balance;

      data["account"] = accountData;
      data["wallet"] = walletData;
      data["virtualNuban"] = action.virtualNuban;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async DeleteAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;

      const action = await AccountDelete(userId);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";

      response.success = success;
      response.message = message;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async RestoreAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const { accountId } = req.params;

      const action = await AccountRestore(accountId);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";

      response.success = success;
      response.message = message;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async UpdateAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;

      const { firstname, lastname, phone, gender, dob, bvn } = req.body;
      const accountUpdate = new AccountUpdate(accountRepository);

      const action = await accountUpdate.init(userId, {
        firstname,
        lastname,
        phone,
        gender,
        dob,
        bvn
      });

      const { account } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      const accountData: any = {};
      accountData["id"] = account._id;
      accountData["firstname"] = account.firstname;
      accountData["lastname"] = account.lastname;
      accountData["email"] = account.email;
      accountData["phone"] = account.phone;
      accountData["gender"] = account.gender;
      accountData["dob"] = account.dob;

      data["account"] = accountData;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async SetupKYC(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const { bvn } = req.body;

      const action = await AccountRegister.SetupKYC(userId, bvn);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "ok";
      const data: any = {};

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async PasswordResetRequestVerification(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { id } = await AccountValidation.passwordResetRequest(req.body);

      const action = await AccountPasswordReset.RequestOTPVerification(id);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = `otp has been sent to ${id}`;
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["code"] = action.otpCode;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async PasswordResetConfirmVerification(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { otp_id, otp_code } = req.body;

      const action = await AccountPasswordReset.ConfirmOTPVerification(otp_id, otp_code);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "OTP verification successful";
      const data: any = {};

      data["otp"] = {};
      data["otp"]["id"] = action.otpId;
      data["otp"]["token"] = action.token;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async PasswordResetNewPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { v_token } = req.body;
      const { password } = await AccountValidation.passwordReset(req.body);

      const action = await AccountPasswordReset.newPassword(v_token, password);

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "password reset successful";
      const data: any = {};

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async UploadProfilePicture(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const pfp = req.file;
      const action: any = await AccountPFP.init(userId, pfp);
      const { userpfp: pfpd } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "OK";
      const data: any = {};

      data["pfp"] = {};
      data["pfp"]["user_id"] = pfpd.userId;
      data["pfp"]["file_id"] = pfpd.fileId;
      data["pfp"]["name"] = pfpd.name;
      data["pfp"]["url"] = pfpd.url;
      data["pfp"]["thumbnail_url"] = pfpd.thumbnailUrl;
      data["pfp"]["file_type"] = pfpd.fileType;
      data["pfp"]["thumbnail_url"] = pfpd.thumbnailUrl;
      data["pfp"]["created_at"] = pfpd.created_at;
      data["pfp"]["updated_at"] = pfpd.updated_at;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
    } catch (e) {
      next(e);
    }
  }

  public static async GetProfilePicture(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = res.locals.authenticated_user;
      const pfp = req.file;
      const action: any = await AccountPFP.getPfP(userId);
      const { userpfp: pfpd } = action;

      const response: any = {};
      const statusCode = 200;
      const success = true;
      const message = "OK";
      const data: any = {};

      data["pfp"] = {};
      data["pfp"]["user_id"] = pfpd.userId;
      data["pfp"]["file_id"] = pfpd.fileId;
      data["pfp"]["name"] = pfpd.name;
      data["pfp"]["url"] = pfpd.url;
      data["pfp"]["thumbnail_url"] = pfpd.thumbnailUrl;
      data["pfp"]["file_type"] = pfpd.fileType;
      data["pfp"]["thumbnail_url"] = pfpd.thumbnailUrl;
      data["pfp"]["created_at"] = pfpd.created_at;
      data["pfp"]["updated_at"] = pfpd.updated_at;

      response.success = success;
      response.message = message;
      response.data = data;

      res.status(statusCode);
      res.json(response);
      console.log(action);
    } catch (e) {
      next(e);
    }
  }

  public static async foo(req: Request, res: Response, next: NextFunction) {
    try {
    } catch (e) {
      next(e);
    }
  }
}

export default AccountController;
