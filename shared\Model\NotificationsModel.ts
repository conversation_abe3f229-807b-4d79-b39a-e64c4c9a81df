import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class Notification extends Model<
  InferAttributes<Notification>,
  InferCreationAttributes<Notification>
> {
  declare id: CreationOptional<string>;
  declare userId: string;
  declare deviceToken: string;

  declare tag: string;
  declare title: string;
  declare message: string;
  declare isRead: Boolean;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Notification.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    deviceToken: {
      field: "device_token",
      type: DataTypes.STRING
    },
    tag: {
      field: "tag",
      type: DataTypes.STRING
    },
    title: {
      field: "title",
      type: DataTypes.STRING
    },
    message: {
      field: "message",
      type: DataTypes.STRING
    },
    isRead: {
      field: "is_read",
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user_notification",
    tableName: "user_notification",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Notification;
