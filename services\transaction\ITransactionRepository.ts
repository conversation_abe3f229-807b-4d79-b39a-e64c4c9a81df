import Transaction from "./Transaction";

type createwTw20 = {
  amount: number;
  charges: number;
  userId: string;
  initiatingWalletId: string;
  transferRecipientId: string;
  anchorPaymentId: string;
  anchorPaymentRef: string;
  status: string;
  bankAccountName: string;
  bankAccountNumber: string;
  bankName: string;
};

type tTransferRecipient = {
  recipientId: string;
  bankCode: string;
  bankName: string;
  bankAccountName: string;
  bankAccountNumber: string;
};

type tLog = {
  transactionId: string;
  walletId: string;
  entryType: string;
  amount: number;
  transactionType: string;
  transactionStatus: string;
  paymentMethod: string;
  statement: string;
  walletBalance: number;
};

interface ITransactionRepository {
  getTransferRecipient(accountNumber: string, bankCode: string): Promise<any>;
  createTransferRecipient(fields: tTransferRecipient): Promise<any>;

  createTransactionWallet2Other(fields: createwTw20): Promise<any>;

  writeLog(fields: tLog): Promise<void>;

  getTransactionHistory(walletId: string, filter: any): Promise<Array<any>>;

  getTransaction(transactionId: string): Promise<any>;
  getTransactionType(transactionId: string): Promise<string>;

  getTransactionWallet2Other(transactionId: string): Promise<any>;
  getTransactionDeposit(transactionId: string): Promise<any>;

  createTransactionWallet2Wallet(fields: any): Promise<any>;

  getTransactionWallet2Wallet(transactionId: string): Promise<any>;
  getTransactionVTU(transactionId: string): Promise<any>;
}

export default ITransactionRepository;
