import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class TransactionSystemCharge extends Model<
  InferAttributes<TransactionSystemCharge>,
  InferCreationAttributes<TransactionSystemCharge>
> {
  declare transactionId: string;
  declare initiator: string;
  declare reason: String;
}

TransactionSystemCharge.init(
  {
    transactionId: {
      field: "transaction_id",
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true
    },
    initiator: {
      field: "initiator",
      type: DataTypes.STRING
    },
    reason: {
      field: "reason",
      type: DataTypes.STRING
    }
  },
  {
    sequelize,
    modelName: "transaction_system_charge",
    tableName: "transaction_system_charge",
    createdAt: false,
    updatedAt: false
  }
);

export default TransactionSystemCharge;
