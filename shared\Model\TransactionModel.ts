import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";
import { sequelize } from "../../app/service-providers/sequelize";

class Transaction extends Model<
  InferAttributes<Transaction>,
  InferCreationAttributes<Transaction>
> {
  declare id: CreationOptional<string>;
  declare transactionType: CreationOptional<string>;
  declare amount: CreationOptional<number>;
  declare charges: CreationOptional<number>;
  declare status: CreationOptional<string>;
  declare initiatingWallet: CreationOptional<string>;

  declare userId: CreationOptional<string>;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

Transaction.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    transactionType: {
      field: "transaction_type",
      type: DataTypes.STRING
    },
    amount: {
      field: "amount",
      type: DataTypes.NUMBER
    },
    charges: {
      field: "charges",
      type: DataTypes.NUMBER,
      defaultValue: 0
    },
    status: {
      field: "status",
      type: DataTypes.STRING,
      defaultValue: "PENDING"
    },
    initiatingWallet: {
      field: "initiating_wallet",
      type: DataTypes.STRING
    },

    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },

    created_at: {
      field: "created_at",
      type: DataTypes.DATE
    },
    updated_at: {
      field: "created_at",
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "transaction",
    tableName: "transaction",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default Transaction;
