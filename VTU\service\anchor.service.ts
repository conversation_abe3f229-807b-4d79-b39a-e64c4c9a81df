import axios, { AxiosResponse } from 'axios';
import { VTU_CONFIG } from '../config/vtu.config';
import { 
  AnchorAirtimeRequest, 
  AnchorAirtimeResponse 
} from '../interfaces/airtime.interface';
import { 
  AnchorDataRequest, 
  AnchorDataResponse 
} from '../interfaces/data.interface';

export class AnchorVTUService {
  private baseURL: string;
  private apiKey: string;

  constructor() {
    this.baseURL = VTU_CONFIG.ANCHOR.BASE_URL;
    this.apiKey = process.env.ANCHOR_API_KEY || '';
    if (!this.apiKey) {
      console.warn('ANCHOR_API_KEY environment variable is not set');
    }
  }

  private getHeaders() {
    return {
      'Content-Type': 'application/json',
      'x-anchor-key': this.apiKey,
      'Accept': 'application/json'
    };
  }

  async purchaseAirtime(request: AnchorAirtimeRequest): Promise<AnchorAirtimeResponse> {
    const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.AIRTIME}`;
    const headers = this.getHeaders();

    console.log('--- Anchor Airtime Purchase Request ---');
    console.log(`URL: POST ${url}`);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    console.log('Payload:', JSON.stringify(request, null, 2));
    console.log('------------------------------------');

    try {
      const response = await axios.post(url, request, {
        headers: headers,
        timeout: 30000
      });

      console.log('--- Anchor Airtime Purchase Response (Success) ---');
      console.log('Status:', response.status);
      console.log('Data:', JSON.stringify(response.data, null, 2));
      console.log('---------------------------------------------');

      return response.data;
    } catch (error: any) {
      console.error('--- Anchor Airtime Purchase Response (Error) ---');
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', JSON.stringify(error.response.data, null, 2));
        console.error('Headers:', JSON.stringify(error.response.headers, null, 2));
      } else if (error.request) {
        console.error('Error: No response received from Anchor.');
        console.error('Request:', error.request);
      } else {
        console.error('Error:', error.message);
      }
      console.error('-------------------------------------------');

      return {
        status: false,
        message: error.response?.data?.message || "Payment provider error"
      };
    }
  }

  async purchaseData(request: AnchorDataRequest): Promise<AnchorDataResponse> {
    const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.DATA}`;
    const headers = this.getHeaders();

    console.log('--- Anchor Data Purchase Request ---');
    console.log(`URL: POST ${url}`);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    console.log('Payload:', JSON.stringify(request, null, 2));
    console.log('----------------------------------');

    try {
      const response: AxiosResponse<AnchorDataResponse> = await axios.post(
        url,
        request,
        {
          headers: headers,
          timeout: 30000
        }
      );

      console.log('--- Anchor Data Purchase Response (Success) ---');
      console.log('Status:', response.status);
      console.log('Data:', JSON.stringify(response.data, null, 2));
      console.log('-------------------------------------------');

      return response.data;

    } catch (error: any) {
      console.error('--- Anchor Data Purchase Response (Error) ---');
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', JSON.stringify(error.response.data, null, 2));
        console.error('Headers:', JSON.stringify(error.response.headers, null, 2));
      } else if (error.request) {
        console.error('Error: No response received from Anchor.');
        console.error('Request:', error.request);
      } else {
        console.error('Error:', error.message);
      }
      console.error('-----------------------------------------');

      return {
        status: false,
        message: error.response?.data?.message || "Payment provider error"
      };
    }
  }

  async verifyTransaction(reference: string): Promise<AnchorAirtimeResponse> {
    try {
      const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.VERIFY}/${reference}`;
      const response: AxiosResponse<AnchorAirtimeResponse> = await axios.get(
        url,
        {
          headers: this.getHeaders(),
          timeout: 15000
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Anchor Verify Error:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      return {
        status: false,
        message: error.response?.data?.message || 'Transaction verification failed'
      };
    }
  }

  async getAirtimeBillers(): Promise<any> {
    try {
      const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.AIRTIME_BILLERS}`;
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 15000
      });
      return response.data;
    } catch (error: any) {
      console.error('Get Billers Error:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      return { 
        status: false, 
        message: 'Failed to get billers',
        error: error.message 
      };
    }
  }

  async getDataBillers(): Promise<any> {
    try {
      const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.DATA_BILLERS}`;
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 15000
      });
      return response.data;
    } catch (error: any) {
      console.error('Get Data Billers Error:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      return { 
        status: false, 
        message: 'Failed to get data billers',
        error: error.message 
      };
    }
  }


  async getDataPlans(network?: string): Promise<any> {
    try {
      const billersResponse = await this.getDataBillers();

      if (!billersResponse || !billersResponse.data) {
        return {
          success: false,
          message: 'Failed to fetch data billers',
        };
      }
  
      const billers = billersResponse.data;
      let dataPlans: any = {};
  
      const processBiller = async (biller: any) => {
        if (biller && biller.attributes && typeof biller.attributes.name === 'string' && biller.attributes.name.length > 0) {
          const networkName = biller.attributes.name.split(' ')[0].toUpperCase();
          const products = await this.getBillerProducts(biller.id);
          if (products) {
            dataPlans[networkName] = products;
          }
        } 
      };
  
      if (network) {
        const biller = billers.find((b: any) => 
          b.attributes && b.attributes.name && b.attributes.name.toUpperCase().includes(network.toUpperCase())
        );
        if (biller) {
          await processBiller(biller);
        }
      } else {
        await Promise.all(billers.map(processBiller));
      }
  
      return {
        success: true,
        message: 'Data plans retrieved successfully',
        data: dataPlans,
      };
    } catch (error: any) {
      console.error('Get Data Plans Error:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      return {
        success: false,
        message: 'Failed to get data plans',
        error: error.message,
      };
    }
  }
  
  private async getBillerProducts(billerId: string): Promise<any[] | null> {
    try {
      const url = `${this.baseURL}${VTU_CONFIG.ANCHOR.ENDPOINTS.DATA_PRODUCTS}/${billerId}/products`;
      const response = await axios.get(url, {
        headers: this.getHeaders(),
        timeout: 15000,
      });
  
      if (response.data && response.data.data) {
        return response.data.data.map((product: any) => {
          const attr = product.attributes;
          return {
            slug: attr.slug,
            price: attr.price,
            name: attr.name,
            description: attr.description,
          };
        });
      }
      return null;
    } catch (error: any) {
      console.error(`Error fetching products for biller ${billerId}:`, {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      return null; 
    }
  }
}