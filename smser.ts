import twilio from "twilio";

const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;

const twilioClient = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

async function send(recipient: string, text: string) {
  const message = await twilioClient.messages.create({
    from: TWILIO_PHONE_NUMBER,
    to: recipient,
    body: text,
  });

  console.log(message.sid);
}

export default { send };
