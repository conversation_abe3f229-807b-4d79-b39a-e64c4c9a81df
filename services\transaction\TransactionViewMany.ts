import ITransactionRepository from "./ITransactionRepository";
import IWalletService from "../../services/wallet/IWalletService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import { log } from "winston";

class TransactionViewMany {
  private walletId: string;
  private userId: string;
  private limit: number;
  private page: number;
  private from: any;
  private to: any;
  private transactionType: string;
  private entryType: string;

  private _repository: ITransactionRepository;
  private _walletService: IWalletService;

  constructor(transactionRepository: ITransactionRepository, walletService: IWalletService) {
    this._repository = transactionRepository;
    this._walletService = walletService;
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  setWalletId(walletId: string) {
    this.walletId = walletId;
  }

  setLimit(limit: any = 30) {
    this.limit = parseInt(limit);
  }

  setPage(page: any = 1) {
    this.page = parseInt(page);
  }

  setTransactionType(transactionType: string) {
    this.transactionType = transactionType.toUpperCase();
  }
  setEntryType(entryType: string) {
    this.entryType = entryType.toUpperCase();
  }

  setFrom(from: any) {
    const date = new Date(from);
    if (isNaN(date.getTime()))
      throw new AppException(domainError.TEST_ERROR, "invalid date string 'from'");

    this.from = date.toISOString();
  }

  setTo(to: any) {
    const date = new Date(to);
    if (isNaN(date.getTime()))
      throw new AppException(domainError.TEST_ERROR, "invalid date string 'to'");

    this.to = date.toISOString();
  }

  async getUserWallet(userId: string) {
    const walletData = await this._walletService.getUserWallet(userId);
    if (!walletData) throw new AppException(domainError.WALLET_AUTHORIZATION_ERROR);
    return walletData.wallet;
  }

  async init(): Promise<any> {
    const wallet: any = await this.getUserWallet(this.userId);

    if (this.from) {
      const to = this.to || new Date().toISOString();

      if (this.from > to)
        throw new AppException(domainError.TEST_ERROR, "invalid date range specification");
    }

    const transactionHistoryData: any = await this._repository.getTransactionHistory(wallet.id, {
      limit: this.limit,
      page: this.page,
      transactionType: this.transactionType,
      entryType: this.entryType,
      from: this.from,
      to: this.to
    });

    return transactionHistoryData;
  }
}

export default TransactionViewMany;
