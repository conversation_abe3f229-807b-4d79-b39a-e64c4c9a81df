import Transaction from "./Transaction";
import ITransactionRepository from "./ITransactionRepository";
import IWalletService from "../wallet/IWalletService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

import AnchorService from "../../AnchorService";
import AnchorModel from "../../shared/Model/AnchorModel";

class TransactionWallet2Other extends Transaction {
  private userId: string;

  private bankCode: string;
  private bankName: string;
  private accountNumber: string;
  private accountName: string;
  private recipientId: number;
  private recipientCode: string;

  private reason: string;
  private initiatorWallet: any;

  private _repository: ITransactionRepository;
  private _walletService: IWalletService;

  constructor(transactionRepository: ITransactionRepository, walletService: IWalletService) {
    super();

    this._repository = transactionRepository;
    this._walletService = walletService;
    this.transactionType = "WITHDRAW";
    this.charges = 20;
  }

  public setUserId(userId: string) {
    this.userId = userId;
  }

  public setWalletId(wallet_id: string) {
    this.initiatingWallet = wallet_id;
  }

  public setAmount(amount: number) {
    this.amount = amount;
  }

  public setBankCode(bankCode: string) {
    this.bankCode = bankCode;
  }

  public setAccountNumber(accountNumber: string) {
    this.accountNumber = accountNumber;
  }

  public setAccountName(accountName: string) {
    this.accountName = accountName;
  }

  private check_if_recipient_is_provided() {
    return !!(this.bankCode && this.accountNumber);
  }

  async setupRecipient(accountNumber: string, bankCode: string) {
    const recipient = await this._repository.getTransferRecipient(accountNumber, bankCode);
    if (recipient)
      return {
        id: recipient.id,
        recipientId: recipient.recipientId,
        bankCode: recipient.bankCode,
        bankName: recipient.bankName,
        accountName: recipient.bankAccountName,
        accountNumber: recipient.bankAccountNumber
      };

    return await this.createNewRecipient(bankCode, accountNumber);
  }

  async createNewRecipient(bankCode: string, accountNumber: string) {
    const VerifyAccountDetails = await AnchorService.VerifyAccountDetails(bankCode, accountNumber);
    if (!VerifyAccountDetails?.data?.attributes?.accountName)
      throw new AppException(domainError.TEST_ERROR, "invalid recipient provided");

    const counterParty = await AnchorService.createCounterparty(
      VerifyAccountDetails.data.attributes.accountName,
      accountNumber,
      bankCode
    );

    if (!counterParty?.data?.id)
      throw new AppException(domainError.TEST_ERROR, "could not complete request at this time");

    const recipient = await this._repository.createTransferRecipient({
      recipientId: counterParty.data.id,
      bankCode: bankCode,
      bankName: counterParty.data.attributes.bank.name,
      bankAccountName: counterParty.data.attributes.accountName,
      bankAccountNumber: accountNumber
    });
    if (!recipient) throw new AppException(domainError.TEST_ERROR, "something went wrong");

    return {
      id: recipient.id,
      recipientId: recipient.recipientId,
      bankCode: recipient.bankCode,
      bankName: recipient.bankName,
      accountName: recipient.bankAccountName,
      accountNumber: recipient.bankAccountNumber
    };
  }

  private async getInitiatorWallet(): Promise<void> {}

  private async createWithdraw() {}

  async debitWallet() {}

  async reverseDebit() {}

  async init() {
    const initiatorWallet = (await this._walletService.getUserWallet(this.userId))?.wallet;
    if (!initiatorWallet) throw new AppException(domainError.WALLET_AUTHORIZATION_ERROR);

    this.initiatorWallet = initiatorWallet;
    this.initiatingWallet = initiatorWallet.id;
    console.log(this.userId);
    console.log(this.initiatorWallet.id);
    console.log(this.initiatorWallet);

    const total = this.amount + this.charges;

    const userAnchor = await AnchorModel.findOne({ where: { userId: this.userId } });
    if (!userAnchor?.depositAccountId)
      throw new AppException(domainError.TEST_ERROR, "your account is not ready for withdrawal ");
    console.log(userAnchor);

    // check wallet balance
    if (total > initiatorWallet.balance)
      throw new AppException(domainError.TEST_ERROR, "Insufficient Balace");

    let recipient = await this.setupRecipient(this.accountNumber, this.bankCode);
    if (!recipient) throw new AppException(domainError.TEST_ERROR, "cannot withdraw to recipient");

    this.recipientId = recipient.id;
    this.recipientCode = recipient.recipientId;
    this.accountName = recipient.accountName;
    this.bankName = recipient.bankName;

    const anchorTransfer = await AnchorService.initiateNIPTransfer(
      total * 100,
      userAnchor.depositAccountId,
      recipient.recipientId
    );

    if (!anchorTransfer?.data)
      throw new AppException(domainError.TEST_ERROR, "could not complete withdrawal at the moment");

    const anchorPaymentId = anchorTransfer.data.id;
    const anchorPaymentRef = anchorTransfer.data.reference;
    const anchorTransferStatus = anchorTransfer.data.attributes.status;

    this.status = anchorTransferStatus;

    const TRANSACTION_TYPE = "WITHDRAW";
    const transaction: any = await this._repository.createTransactionWallet2Other({
      amount: this.amount,
      charges: this.charges,
      userId: this.userId,
      initiatingWalletId: this.initiatingWallet,
      transferRecipientId: recipient.id,
      anchorPaymentId: anchorPaymentId,
      anchorPaymentRef: anchorPaymentRef,
      status: anchorTransferStatus,
      bankAccountName: recipient.accountName,
      bankAccountNumber: recipient.accountNumber,
      bankName: recipient.bankName
    });
    if (!transaction)
      throw new AppException(domainError.TEST_ERROR, "could not complete withdrawal at the moment");

    const { id: transactionId, created_at } = transaction;
    this.id = transactionId;
    this.created_at = created_at;

    if (anchorTransferStatus !== "FAILED") {
      this.initiatorWallet = (
        await this._walletService.withdraw(this.initiatingWallet, total)
      ).wallet;

      let source = recipient.bankName;
      let transactionStatement = "withdraw to bank";
      transactionStatement = `bank transfer to ${recipient.accountName} ${this.bankName} `;
      let walletBalance;

      await this._repository.writeLog({
        transactionId: transactionId,
        walletId: this.initiatingWallet,
        entryType: "DEBIT",
        amount: this.amount,
        transactionStatus: anchorTransferStatus,
        statement: transactionStatement,
        paymentMethod: "TRANSFER",
        transactionType: "WITHDRAW",
        walletBalance: this.initiatorWallet.balance
      });
    }

    return { transaction: this.serialize(), wallet: this.initiatorWallet };
  }

  serialize(): any {
    return {
      id: this.id,
      transactionType: this.transactionType,
      amount: this.amount,
      charges: this.charges,
      status: this.status,
      initiatingWallet: this.initiatingWallet,
      userId: this.userId,
      accountName: this.accountName,
      accountNumber: this.accountNumber,
      bankCode: this.bankCode,
      bankName: this.bankName,
      transferRecipientId: this.recipientId,
      transferRecipientCode: this.recipientCode,
      reason: this.reason,
      created_at: this.created_at
    };
  }
}

export default TransactionWallet2Other;

/*
TransactionWallet2Other {
  id: '1edc2e2b-2d8c-4579-9fc3-30b49864ed9b',
  transactionType: 'WITHDRAW',
  amount: 1000,
  charges: 20,
  status: undefined,
  initiatingWallet: 'f51a08b3-d0f1-478a-9cff-74e020ee4cd2',
  created_at: 2025-07-01T15:23:40.840Z,
  userId: '0d45d7bf-0f92-497b-aa5e-601e73b7ee08',
  bankCode: '000013',
  bankName: undefined,
  accountNumber: '**********',
  accountName: undefined,
  recipientId: undefined,
  recipientCode: undefined,
  reason: undefined,
  initiatorWallet: {
    wallet: Wallet {
      id: 'f51a08b3-d0f1-478a-9cff-74e020ee4cd2',
      balance: 8000
    }
  },
  _repository: TransactionRepository {},
  _walletService: WalletService {}
}
*/
