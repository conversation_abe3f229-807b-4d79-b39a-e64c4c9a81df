import AnchorService from "../../AnchorService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class SupportResolveAccountDetails {
  constructor() {}

  async init(bankCode: string, accountNumber: string): Promise<any> {
    let accountDetails = await AnchorService.VerifyAccountDetails(bankCode, accountNumber);
    if (!accountDetails?.data)
      throw new AppException(
        domainError.TEST_ERROR,
        "could not resolve account details at the moment "
      );

    accountDetails = accountDetails.data;

    return {
      account: {
        accountName: accountDetails.attributes.accountName,
        accountNumber: accountDetails.attributes.accountNumber
      },
      bank: accountDetails.attributes.bank
    };
  }
}

export default SupportResolveAccountDetails;
