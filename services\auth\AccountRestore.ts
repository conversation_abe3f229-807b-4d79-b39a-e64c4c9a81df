import AppException from "../../AppException";
import { domainError } from "../../domainError";
import Usermodel from "../../shared/Model/UserModel";

async function init(userId: string) {
  const account = await Usermodel.findByPk(userId);
  if (!account) throw new AppException(domainError.NOT_FOUND, "user does not exist");

  await Usermodel.update(
    { deactivated: false },
    {
      where: {
        id: userId
      }
    }
  );
}

export default init;
