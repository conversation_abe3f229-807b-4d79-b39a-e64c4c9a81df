'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('transaction_wallet_deposit', {
      transaction_id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      deposit_type: {
        type: Sequelize.STRING,
        allowNull: true
      },
      virtual_nuban_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      anchor_payment_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      anchor_payment_ref: {
        type: Sequelize.STRING,
        allowNull: true
      },
      paid_at: {
        type: Sequelize.STRING,
        allowNull: true
      },
      account_number: {
        type: Sequelize.STRING,
        allowNull: true
      },
      account_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      bank_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      reason: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: ""
      }
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('transaction_wallet_deposit');
  }
};
