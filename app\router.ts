import { Request, Response, NextFunction } from "express";
import jwt from "../libraries/jwt";
import AccountAPI from "../services/auth/AccountAPI";
import WalletAPI from "../services/wallet/WalletAPI";
import TransactionAPI from "../services/transaction/TransactionAPI";
import SupportAPI from "../services/support/SupportAPI";
import BeneficiaryAP<PERSON> from "../services/beneficiary/BeneficiaryAPI";
import PhoneAP<PERSON> from "../services/saved/PhoneAPI";
import WaitlistAPI from "../services/waitlist/WaitlistAPI";
import ContactAPI from "../services/contact/ContactAPI";
import vtuRoutes from "../VTU/routes/index";
import config from "config";

import AnchorService from "../AnchorService";
import AnchorWebHooks from "../AnchorWebHooks";

import "../AnchorFailureRetry";
import "../PushNotification";

const router = require("express").Router();

router.get("/", function(req: Request, res: Response, next: NextFunction) {
  res.send("Welcome to Agentpesa API");
});

router.get("/error", function(req: Request, res: Response, next: NextFunction) {
  throw new Error("Server ran into an error");
});
router.use("/vtu", vtuRoutes);
router.use("/accounts", AccountAPI);
router.use("/wallet", WalletAPI);
router.use("/transactions", TransactionAPI);
router.use("/beneficiaries", BeneficiaryAPI);
router.use("/saved/phone", PhoneAPI);
router.use("/support", SupportAPI);
router.use("/waitlist", WaitlistAPI);
router.use("/contact", ContactAPI);

router.post("/webhooks/anchor", async function(req: Request, res: Response, next: NextFunction) {
  try {
    console.log("################## WEBHOOK ##################");
    console.log("################## WEBHOOK REQUEST BODY ##################");
    console.log(req.body);
    const data = req.body;
    const type = data.type;
    console.log(type);

    switch (type) {
      case "book.transfer.failed":
        AnchorWebHooks.BookTransferFailed(data);
        break;

      case "book.transfer.successful":
        AnchorWebHooks.BookTransferSuccessful(data);
        break;

      case "nip.transfer.failed":
        AnchorWebHooks.NIPTransferFailed(data);
        break;

      case "nip.transfer.successful":
        AnchorWebHooks.NIPTransferSuccessful(data);
        break;

      case "payment.settled":
        AnchorWebHooks.paymentSettled(data);
        break;

      case "virtualNuban.opened":
        AnchorWebHooks.virtualNubanOpened(data);
        break;

      case "account.opened":
        AnchorWebHooks.accountOpened(data);
        break;

      case "customer.identification.approved":
        AnchorWebHooks.customerIdentificationApproved(data);
        break;

      case "customer.identification.rejected":
        break;

      case "customer.identification.error":
        break;
    }

    //
    res.sendStatus(200);
  } catch (err) {
    console.error(err);
  }
});

export default router;
