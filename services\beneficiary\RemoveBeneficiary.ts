import IBeneficiaryRepository from "./IBeneficiaryRepository";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

class RemoveBeneficiary {
  private _repository: IBeneficiaryRepository;

  constructor(beneficiaryRepository: IBeneficiaryRepository) {
    this._repository = beneficiaryRepository;
  }

  async init(userId: string, beneficiaryId: string): Promise<any> {
    await this._repository.removeBeneficiary(userId, beneficiaryId);
  }
}

export default RemoveBeneficiary;
