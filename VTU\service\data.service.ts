import { v4 as uuidv4 } from "uuid";
import { AnchorVTUService } from "./anchor.service";
import { VTURepository } from "../repository/vtu.repository";
import { VTURepository as VTURepository2 } from "../repository/vtu.repository2";
import { VTUValidation } from "../validations/vtu.validation";
import { DataValidation } from "../validations/data.validation";
import { VTU_CONFIG, getNetworkFromPhone } from "../config/vtu.config";
import { DataRequest, DataResponse, AnchorDataRequest } from "../interfaces/data.interface";
import WalletService from "../../services/wallet/WalletService";
import AppException from "../../AppException";
import { domainError } from "../../domainError";
import AnchorModel from "../../shared/Model/AnchorModel";
import TransactionLogModel from "../../shared/Model/TransactionLogModel";

export class DataService {
  private anchorService: AnchorVTUService;
  private vtuRepository: VTURepository;
  private vtuRepository2: VTURepository2;

  constructor() {
    this.anchorService = new AnchorVTUService();
    this.vtuRepository = new VTURepository();
    this.vtuRepository2 = new VTURepository2();
  }

  async purchaseData(request: DataRequest): Promise<DataResponse> {
    try {
      // Validate request (remove network from validation schema)
      const validation = DataValidation.validateDataRequest({
        ...request,
        network: undefined
      });
      if (validation.error) {
        return {
          success: false,
          message: validation.error.details[0].message
        };
      }

      // Validate and format phone number
      const phoneValidation = VTUValidation.validatePhoneNumber(request.phoneNumber);
      if (!phoneValidation.isValid) {
        return {
          success: false,
          message: phoneValidation.error || "Invalid phone number"
        };
      }

      // Auto-detect network from phone number
      const network = getNetworkFromPhone(request.phoneNumber);
      if (!network || network === "UNKNOWN") {
        return {
          success: false,
          message: "Unable to detect network provider from phone number"
        };
      }

      // Get data plan details from API
      const dataPlan = await this.getDataPlanDetails(request.dataCode, network);
      console.log("######################################");
      console.log("######################################");
      console.log("######################################");
      console.log("######################################");
      console.log("######################################");
      console.log(dataPlan);

      if (!dataPlan || !dataPlan.price) {
        return {
          success: false,
          message: "Invalid data plan selected or plan not available"
        };
      }

      // The amount from the API is in Kobo. Convert to Naira for internal use.
      const planAmountInNaira = dataPlan.price.minimumAmount / 100;

      // get user anchor
      // confirm deposit id
      // get user wallet
      const senderAnchor = await AnchorModel.findOne({ where: { userId: request.userId } });
      if (!senderAnchor?.depositAccountId)
        throw new AppException(
          domainError.TEST_ERROR,
          "your account setup is not complete and can not run transaction"
        );

      // Check user's wallet balance
      const walletService = new WalletService();
      const walletResponse = await walletService.getUserWallet(request.userId);
      if (!walletResponse || !walletResponse.wallet) {
        return {
          success: false,
          message: "User wallet not found"
        };
      }
      const userWallet = walletResponse.wallet;

      console.log("--- Balance Check ---");
      console.log(`User Wallet Balance: ${userWallet.balance}`);
      console.log(`Data Plan Cost (Naira): ${planAmountInNaira}`);
      console.log("---------------------");

      if (userWallet.balance < planAmountInNaira) {
        return {
          success: false,
          message: "Insufficient wallet balance"
        };
      }

      // Generate unique reference
      const reference = `DATA_${Date.now()}_${uuidv4().substring(0, 8)}`;

      // Create transaction record
      const transaction = await this.vtuRepository.createTransaction({
        userId: request.userId,
        serviceType: VTU_CONFIG.SERVICE_TYPES.DATA,
        phoneNumber: phoneValidation.formatted,
        amount: planAmountInNaira, // Use the correct amount in Naira
        network,
        reference,
        status: VTU_CONFIG.TRANSACTION_STATUS.PROCESSING
      });

      // Prepare Anchor API request
      const networkConfig =
        VTU_CONFIG.ANCHOR.NETWORKS[network as keyof typeof VTU_CONFIG.ANCHOR.NETWORKS];
      if (!networkConfig) {
        await this.vtuRepository.updateTransaction(transaction.id, {
          status: VTU_CONFIG.TRANSACTION_STATUS.FAILED,
          anchorResponse: "Unsupported network provider"
        });

        return {
          success: false,
          message: "Unsupported network provider"
        };
      }

      const anchorRequest: AnchorDataRequest = {
        data: {
          type: "Data",
          attributes: {
            phoneNumber: phoneValidation.formatted,
            amount: dataPlan.price.minimumAmount, // Use the Kobo amount for the API
            productSlug: dataPlan.slug,
            reference: reference
          },
          relationships: {
            account: {
              data: {
                type: "DepositAccount",
                id: "**************-anc_acc" // Hardcoded for testing
              }
            }
          }
        }
      };

      console.log("Anchor Data Request Payload:", JSON.stringify(anchorRequest, null, 2));

      const anchorResponse = await this.anchorService.purchaseData(anchorRequest);

      // Update transaction with response
      const isSuccess = !!anchorResponse.data;

      const status = isSuccess
        ? VTU_CONFIG.TRANSACTION_STATUS.SUCCESSFUL
        : VTU_CONFIG.TRANSACTION_STATUS.FAILED;

      await this.vtuRepository.updateTransaction(transaction.id, {
        status,
        providerReference: anchorResponse.data?.ident,
        anchorResponse: JSON.stringify(anchorResponse)
      });

      let transaction2: any;
      let wallet: any;

      // Deduct money from wallet only if transaction is successful
      if (isSuccess) {
        try {
          transaction2 = await this.vtuRepository2.createTransaction({
            transactionType: "DATA",
            amount: planAmountInNaira,
            status: "SUCCESS",
            initiatingWalletId: userWallet.id,
            userId: request.userId,
            phoneNumber: phoneValidation.formatted,
            network,
            provider: network.toLowerCase(),
            reference,
            providerReference: anchorResponse.data?.ident,
            plan: dataPlan.name
          });

          wallet = (await walletService.withdraw(userWallet.id, planAmountInNaira)).wallet; // Use the Naira amount for withdrawal

          await TransactionLogModel.create({
            transactionId: transaction2.id,
            walletId: userWallet.id,
            entryType: "DEBIT",
            amount: planAmountInNaira,
            transactionType: "DATA",
            transactionStatus: "SUCCESS",
            paymentMethod: "WALLET",
            statement: "data subscription purchase",
            balance: wallet.balance
          });
        } catch (e) {
          const withdrawError: any = e;
          console.error("Wallet withdrawal failed after successful VTU:", withdrawError.message);
        }
        return {
          success: true,
          message: "Data purchase successful",
          data: {
            reference,
            status: "SUCCESS",
            plan: dataPlan.name,
            amount: planAmountInNaira,
            phoneNumber: phoneValidation.formatted,
            network,
            transactionId: transaction.id,
            created_at: transaction2.created_at,
            wallet: {
              id: wallet.id,
              balance: wallet.balance
            }
          }
        };
      } else {
        return {
          success: false,
          message: anchorResponse.message || "Data purchase failed",
          error: anchorResponse.message
        };
      }
    } catch (e) {
      const error: any = e;
      console.error("Data Service Error:", error.message);
      return {
        success: false,
        message: "An error occurred while processing your request",
        error: error.message
      };
    }
  }

  private async getDataPlanDetails(
    dataCode: string,
    network: string
  ): Promise<{
    slug: string;
    price: {
      type: string;
      minimumAmount: number;
      maximumAmount: number;
      currency: string;
    };
    name: string;
    description: string;
  } | null> {
    try {
      // Get fresh data plans from API
      const plansResponse = await this.anchorService.getDataPlans(network);

      if (!plansResponse.success || !plansResponse.data[network]) {
        console.error(`Failed to get data plans for ${network}`);
        return null;
      }

      const networkPlans = plansResponse.data[network];
      const dataPlan = networkPlans.find((plan: any) => plan.slug === dataCode);

      return dataPlan || null;
    } catch (e) {
      const error: any = e;
      console.error("Error getting data plan details:", error.message);
      return null;
    }
  }

  async getDataPlans(network: string) {
    try {
      // Use the Anchor service to get live data plans
      const result = await this.anchorService.getDataPlans(network);
      return result;
    } catch (e) {
      const error: any = e;
      console.error("Get Data Plans Error:", error.message);
      return {
        success: false,
        message: "Failed to get data plans",
        error: error.message
      };
    }
  }
}
