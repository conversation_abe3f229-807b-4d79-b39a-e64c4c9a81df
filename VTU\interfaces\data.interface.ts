export interface DataRequest {
  phoneNumber: string;
  dataCode: string;
  userId: string;
}

export interface DataResponse {
  success: boolean;
  message: string;
  data?: {
    reference: string;
    status: string;
    plan: string;
    amount: number;
    phoneNumber: string;
    network: string;
    transactionId?: string;
    created_at: string;
    wallet?: {
      id: string;
      balance: string;
    };
  };
  error?: string;
}

export interface AnchorDataRequest {
  data: {
    type: "Data";
    attributes: {
      phoneNumber: string;
      amount: number;
      productSlug: string;
      reference: string;
    };
    relationships: {
      account: {
        data: {
          type: "DepositAccount";
          id: string;
        };
      };
    };
  };
}

export interface AnchorDataResponse {
  status: boolean;
  message: string;
  data?: {
    amount: number;
    phone: string;
    status: string;
    ident: string;
    api_response: string;
  };
}
