import { Router, Request, Response, NextFunction } from "express";
import PhoneController from "./PhoneController";
import jwt from "../../libraries/jwt";
import AppException from "../../AppException";
import { domainError } from "../../domainError";

import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";

const router: Router = Router();

router.post("/", AuthProtectionMiddleware, PhoneController.addPhone);
router.get("/", AuthProtectionMiddleware, PhoneController.getPhones);
router.get("/:alias", AuthProtectionMiddleware, PhoneController.getPhone);
router.put("/:alias", AuthProtectionMiddleware, PhoneController.updatePhone);
router.delete("/:alias", AuthProtectionMiddleware, PhoneController.deletePhone);

export default router;
