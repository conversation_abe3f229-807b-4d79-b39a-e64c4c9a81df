import { Router } from "express";
import AccountController from "./AccountController";
import AuthProtectionMiddleware from "../../AuthProtectionMiddleware";
import multer from "multer";

const multerStorage = multer.memoryStorage();
const multerUpload = multer({ storage: multerStorage });

const router: Router = Router();

router.post("/", AccountController.CreateAccount);
router.get("/", AuthProtectionMiddleware, AccountController.ViewAccount);
router.post("/kyc", AuthProtectionMiddleware, AccountController.SetupKYC);
router.put("/", AuthProtectionMiddleware, AccountController.UpdateAccount);

router.post(
  "/pfp",
  AuthProtectionMiddleware,
  multerUpload.single("pfp"),
  AccountController.UploadProfilePicture
);
router.get("/pfp", AuthProtectionMiddleware, AccountController.GetProfilePicture);

router.delete("/", AuthProtectionMiddleware, AccountController.DeleteAccount);
router.put("/:accountId/restore", AccountController.RestoreAccount);

router.post("/verification/phone", AccountController.RequestPhoneVerification);
router.post("/verification/phone/confirm", AccountController.ConfirmPhoneVerification);

router.post("/verification/email", AccountController.RequestEmailVerification);
router.post("/verification/email/confirm", AccountController.ConfirmEmailVerification);

router.post("/login", AccountController.LoginAccount);
router.post("/refresh", AccountController.RefreshToken);
router.post("/device-token", AuthProtectionMiddleware, AccountController.UpdateDeviceToken);

router.post("/password-reset", AccountController.PasswordResetRequestVerification);
router.post("/password-reset/confirm", AccountController.PasswordResetConfirmVerification);
router.post("/password", AccountController.PasswordResetNewPassword);

export default router;
