import IAccountRepository from "./IAccountRepository";
import Account from "./Account";
import UserModel from "../../shared/Model/UserModel";
import UserKYCModel from "../../shared/Model/UserKYCModel";

import AppException from "../../AppException";
import { domainError } from "../../domainError";

class AccountRepository implements IAccountRepository {
  async emailExists(email: string): Promise<boolean> {
    return !!(await UserModel.findOne({ where: { email } }));
  }

  async phoneExists(phone: string): Promise<boolean> {
    return !!(await UserModel.findOne({ where: { phone } }));
  }

  async bvnExists(bvn: string): Promise<boolean> {
    return !!(await UserKYCModel.findOne({ where: { bvn: bvn } }));
  }

  async createAccount(
    firstname: string,
    lastname: string,
    gender: string,
    dob: string,
    email: string,
    phone: string,
    password: string
  ): Promise<Account> {
    const user = await UserModel.create({
      firstname: firstname,
      lastname: lastname,
      phone: phone,
      email: email,
      gender: gender,
      dob: dob,
      password: password
    });

    return new Account(
      user.id,
      user.firstname,
      user.lastname,
      user.email,
      user.phone,
      user.gender,
      user.dob,
      user.password,
      user.created_at
    );
  }

  async saveDeviceInfo(
    userId: string,
    deviceId: string,
    deviceOS: string,
    deviceToken: string
  ): Promise<void> {
    await UserModel.update(
      {
        deviceId: deviceId,
        deviceOS: deviceOS,
        deviceToken: deviceToken
      },
      { where: { id: userId } }
    );
  }

  async getAccountByEmail(email: string): Promise<Account | null> {
    const user = await UserModel.findOne({ where: { email } });
    if (user?.deactivated) throw new AppException(domainError.TEST_ERROR, "deactivated account");

    if (user)
      return new Account(
        user.id,
        user.firstname,
        user.lastname,
        user.email,
        user.phone,
        user.gender,
        user.dob,
        user.password,
        user.created_at
      );
    else return null;
  }

  async getAccountById(id: string): Promise<Account | null> {
    const user = await UserModel.findOne({ where: { id } });
    if (user?.deactivated) throw new AppException(domainError.TEST_ERROR, "deactivated account");

    if (user)
      return new Account(
        user.id,
        user.firstname,
        user.lastname,
        user.email,
        user.phone,
        user.gender,
        user.dob,
        user.password,
        user.created_at
      );
    else return null;
  }

  async updateAccountPassword(id: string, password: string): Promise<void> {
    await UserModel.update({ password: password }, { where: { id: id } });
  }
}

export default AccountRepository;

/*

import IWalletRepository from "./IWalletRepository";
import MModels from "../shared/Model";
import Wallet from "./Wallet";
import AppException from "../AppException";
import { domainError } from "../domainError";

const { Wallet: WalletModel, User: Usermodel } = MModels;

class WalletRepository implements IWalletRepository {
  async createWallet(user_id: string): Promise<Wallet> {
    const { id: walletId } = await WalletModel.create({ user_id: user_id });

    const data: any = await WalletModel.findOne({
      attributes: ["id", "balance"],
      where: { id: walletId },
      include: [
        {
          model: Usermodel,
          attributes: ["id", "name", "email"],
          as: "user"
        }
      ]
    });

    const { id, balance, user } = data;
    const { id: userId, name: userName, email: userEmail } = user;

    return new Wallet(id, balance, userName, userEmail);
  }

  async getWallet(wallet_id: string): Promise<Wallet> {
    const data: any = await WalletModel.findOne({
      attributes: ["id", "balance"],
      where: { id: wallet_id },
      include: [
        {
          model: Usermodel,
          attributes: ["id", "name", "email"],
          as: "user"
        }
      ]
    });

    if (!data) throw new AppException(domainError.NOT_FOUND, `wallet ${wallet_id} does not exist`); // ?????????

    const { id, balance, user } = data;
    const { id: userId, name: userName, email: userEmail } = user;

    return new Wallet(id, balance, userName, userEmail);
  }

  async getUserWallet(user_id: string): Promise<Wallet | null> {
    const data: any = await WalletModel.findOne({
      attributes: ["id", "balance"],
      where: { user_id: user_id },
      include: [
        {
          model: Usermodel,
          attributes: ["id", "name", "email"],
          as: "user"
        }
      ]
    });
    if (!data) return null;

    const { id, balance, user } = data;
    const { id: userId, name: userName, email: userEmail } = user;

    return new Wallet(id, balance, userName, userEmail);
  }

  async save(wallet: Wallet) {
    const { id, balance } = wallet.serialize();
    WalletModel.update({ balance: balance }, { where: { id } });
  }
}

export default WalletRepository;
*/
