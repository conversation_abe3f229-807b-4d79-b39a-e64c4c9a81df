name: 🚀 Deploy to Droplet

on:
  push:
    branches:
      - main  # Change this if your production branch is different

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Deploy over SSH
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.PROD_USER }}@${{ secrets.PROD_HOST }} << 'EOF'
            # Load NVM and use correct Node version
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            nvm use 21

            echo "✅ Node version:"
            node -v
            echo "✅ NPM version:"
            npm -v

            # Navigate to project
            cd /root/AgentPesa-Backend

            # Pull latest code
            git pull origin main

            # Install dependencies
            npm install

            # Rebuild or recompile if needed (optional)
            # npm run build

            # Restart PM2 using ecosystem config
            pm2 delete agentpesa || true
            pm2 start ecosystem.config.js

            # Optional: save PM2 state for reboot
            pm2 save

            echo "✅ Deployment completed"
          EOF
