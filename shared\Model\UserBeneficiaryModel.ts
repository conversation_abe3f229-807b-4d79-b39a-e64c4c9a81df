import {
  DataTypes,
  Model,
  UUIDV4,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional
} from "sequelize";

import { sequelize } from "../../app/service-providers/sequelize";

class UserBeneficiary extends Model<
  InferAttributes<UserBeneficiary>,
  InferCreationAttributes<UserBeneficiary>
> {
  declare id: CreationOptional<string>;
  declare userId: string;

  declare bankCode: string;
  declare bankName: string;
  declare bankAccountName: string;
  declare bankAccountNumber: string;

  declare alias: string;

  declare created_at: CreationOptional<string>;
  declare updated_at: CreationOptional<string>;
}

UserBeneficiary.init(
  {
    id: {
      field: "id",
      type: DataTypes.UUID,
      defaultValue: UUIDV4,
      primaryKey: true
    },
    userId: {
      field: "user_id",
      type: DataTypes.STRING
    },
    bankCode: {
      field: "bank_code",
      type: DataTypes.STRING
    },
    bankName: {
      field: "bank_name",
      type: DataTypes.STRING
    },
    bankAccountName: {
      field: "bank_account_name",
      type: DataTypes.STRING
    },
    bankAccountNumber: {
      field: "bank_account_number",
      type: DataTypes.STRING
    },
    alias: {
      field: "alias",
      type: DataTypes.STRING
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  },
  {
    sequelize,
    modelName: "user_beneficiary",
    tableName: "user_beneficiary",
    createdAt: "created_at",
    updatedAt: "updated_at"
  }
);

export default UserBeneficiary;
